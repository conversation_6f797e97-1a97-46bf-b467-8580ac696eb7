{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/lib/db.ts"], "sourcesContent": ["import { Pool, PoolConfig } from 'pg';\n\n// Parse the service account credentials from environment variable\nlet credentials: any = null;\ntry {\n  if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {\n    credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);\n  }\n} catch (error) {\n  console.error('Error parsing Google credentials:', error);\n}\n\n// Cloud SQL connection configuration\nconst getPoolConfig = (): PoolConfig => {\n  // Log environment variables for debugging\n  console.log('Environment variables:');\n  console.log('USE_CLOUD_SQL_AUTH_PROXY:', process.env.USE_CLOUD_SQL_AUTH_PROXY);\n  console.log('DB_HOST:', process.env.DB_HOST);\n  console.log('DB_PORT:', process.env.DB_PORT);\n  console.log('NODE_ENV:', process.env.NODE_ENV);\n\n  // Force direct connection regardless of environment variable\n  // This is a temporary fix to ensure we're using direct connection\n  const config = {\n    host: '***********', // Hardcoded for now\n    database: process.env.DB_NAME || 'postgres',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASS || 'Plotpointe!@3456',\n    port: 5432, // Hardcoded for now\n    ssl: { rejectUnauthorized: false } // Enable SSL with rejectUnauthorized: false\n  };\n\n  console.log('Using database config:', JSON.stringify(config, null, 2));\n  return config;\n};\n\n// Create the connection pool\nconst pool = new Pool(getPoolConfig());\n\n// Add error handler to log connection issues\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nexport default pool;\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,kEAAkE;AAClE,IAAI,cAAmB;AACvB,IAAI;IACF,IAAI,QAAQ,GAAG,CAAC,mCAAmC,EAAE;QACnD,cAAc,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,mCAAmC;IAC1E;AACF,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,qCAAqC;AACrD;AAEA,qCAAqC;AACrC,MAAM,gBAAgB;IACpB,0CAA0C;IAC1C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,GAAG,CAAC,wBAAwB;IAC7E,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC;IAEZ,6DAA6D;IAC7D,kEAAkE;IAClE,MAAM,SAAS;QACb,MAAM;QACN,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM;QACN,KAAK;YAAE,oBAAoB;QAAM,EAAE,4CAA4C;IACjF;IAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,QAAQ,MAAM;IACnE,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;AAEtB,6CAA6C;AAC7C,KAAK,EAAE,CAAC,SAAS,CAAC;IAChB,QAAQ,KAAK,CAAC,mCAAmC;IACjD,QAAQ,IAAI,CAAC,CAAC;AAChB;uCAEe", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport pool from '@/lib/db';\n\n// Helper function to extract YouTube video ID from various URL formats\nfunction extractYoutubeVideoId(url: string): string | null {\n  if (!url) return null;\n\n  try {\n    // Handle youtu.be format\n    if (url.includes('youtu.be/')) {\n      const id = url.split('youtu.be/')[1]?.split(/[?#]/)[0];\n      return id || null;\n    }\n\n    // Handle youtube.com/shorts format\n    if (url.includes('/shorts/')) {\n      const id = url.split('/shorts/')[1]?.split(/[?#]/)[0];\n      return id || null;\n    }\n\n    // Handle youtube.com/watch?v= format\n    if (url.includes('/watch?')) {\n      const urlObj = new URL(url);\n      return urlObj.searchParams.get('v');\n    }\n\n    // Handle other potential formats\n    const regex = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?)\\/|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/i;\n    const match = url.match(regex);\n    return match ? match[1] : null;\n  } catch (error) {\n    console.error('Error extracting YouTube video ID:', error);\n    return null;\n  }\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    // Await params before using its properties\n    const { id } = await params;\n    const projectId = id;\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Query the database for the project\n      const projectResult = await client.query(\n        `SELECT id, name, description, created_at, created_by, members\n         FROM src_projects\n         WHERE id = $1`,\n        [projectId]\n      );\n\n      if (projectResult.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Project not found' },\n          { status: 404 }\n        );\n      }\n\n      const project = projectResult.rows[0];\n\n      // Query the database for the project variables\n      const variablesResult = await client.query(\n        `SELECT id, name, note, created_at\n         FROM src_project_variables\n         WHERE project_id = $1\n         ORDER BY created_at`,\n        [projectId]\n      );\n\n      const variables = variablesResult.rows;\n\n      // For each variable, get its resources\n      for (const variable of variables) {\n        // First, get resources from src_variable_resources table\n        const resourcesResult = await client.query(\n          `SELECT id, google_doc_link, youtube_url, note, account, created_at\n           FROM src_variable_resources\n           WHERE variable_id = $1\n           ORDER BY created_at`,\n          [variable.id]\n        );\n\n        // Debug: Log the resources for this variable\n        console.log(`Resources for variable ${variable.id} (${variable.name}) from src_variable_resources:`, resourcesResult.rows);\n\n        variable.resources = resourcesResult.rows;\n\n        // For each resource, check if there's a corresponding script entry with a trello_card_id\n        for (const resource of variable.resources) {\n          if (resource.google_doc_link) {\n            try {\n              // Look for a script entry with this Google Doc link (without variable_id)\n              const scriptResult = await client.query(\n                `SELECT id, trello_card_id, google_doc_link\n                 FROM script\n                 WHERE google_doc_link = $1`,\n                [resource.google_doc_link]\n              );\n\n              console.log(`Script entries for resource ${resource.id} with Google Doc link ${resource.google_doc_link}:`, scriptResult.rows);\n\n              // If we found a script entry, add the trello_card_id to the resource\n              if (scriptResult.rows.length > 0) {\n                resource.trello_card_id = scriptResult.rows[0].trello_card_id;\n                resource.script_id = scriptResult.rows[0].id;\n                console.log(`Added trello_card_id ${resource.trello_card_id} to resource ${resource.id}`);\n              }\n            } catch (error) {\n              console.error(`Error querying script table for Google Doc link ${resource.google_doc_link}:`, error);\n            }\n\n            // Also check if there's a YouTube URL in the note field\n            if (resource.note && typeof resource.note === 'string') {\n              // Extract YouTube URL from note if present\n              const youtubeUrlMatch = resource.note.match(/https?:\\/\\/(www\\.)?(youtube\\.com|youtu\\.be)\\/[^\\s]+/);\n              if (youtubeUrlMatch) {\n                const youtubeUrl = youtubeUrlMatch[0];\n                console.log(`Found YouTube URL in note for resource ${resource.id}: ${youtubeUrl}`);\n                resource.youtube_url_from_note = youtubeUrl;\n              }\n            }\n          }\n        }\n\n        // Now, check if there are any YouTube URLs in the video table for this variable\n        const videoResult = await client.query(\n          `SELECT id, url, writer_id, account_id, trello_card_id\n           FROM video\n           WHERE variable_id = $1 AND url IS NOT NULL AND url != ''`,\n          [variable.id]\n        );\n\n        console.log(`YouTube URLs from video table for variable ${variable.id}:`, videoResult.rows);\n\n        // Also check for videos with trello_card_ids that match our resources\n        const trelloCardIds = variable.resources\n          .filter(resource => resource.trello_card_id)\n          .map(resource => resource.trello_card_id);\n\n        if (trelloCardIds.length > 0) {\n          const trelloVideoResult = await client.query(\n            `SELECT id, url, writer_id, account_id, trello_card_id\n             FROM video\n             WHERE trello_card_id = ANY($1) AND url IS NOT NULL AND url != ''`,\n            [trelloCardIds]\n          );\n\n          console.log(`YouTube URLs from video table for trello_card_ids:`, trelloVideoResult.rows);\n\n          // Add these videos to our videoResult if they're not already there\n          for (const video of trelloVideoResult.rows) {\n            if (!videoResult.rows.some(v => v.id === video.id)) {\n              videoResult.rows.push(video);\n            }\n          }\n        }\n\n        // If we found YouTube URLs in the video table, update the resources\n        if (videoResult.rows.length > 0) {\n          // For each resource, check if there's a matching YouTube URL in the video table\n          for (const resource of variable.resources) {\n            // Try to find a matching video by trello_card_id if it exists\n            const matchingVideo = videoResult.rows.find(video =>\n              (resource.trello_card_id && video.trello_card_id &&\n               resource.trello_card_id === video.trello_card_id)\n            );\n\n            if (matchingVideo) {\n              // Update the resource with the YouTube URL from the video table\n              resource.youtube_url = matchingVideo.url;\n              resource.video_id = matchingVideo.id;\n              resource.from_video_table = true;\n              console.log(`Updated resource ${resource.id} with YouTube URL from video table:`, matchingVideo.url);\n            } else if (resource.youtube_url_from_note) {\n              // Check if there's a video with a URL that matches the one from the note\n              const videoIdFromNote = extractYoutubeVideoId(resource.youtube_url_from_note);\n              if (videoIdFromNote) {\n                const matchingVideoFromNote = videoResult.rows.find(video => {\n                  const videoId = extractYoutubeVideoId(video.url);\n                  return videoId === videoIdFromNote;\n                });\n\n                if (matchingVideoFromNote) {\n                  resource.youtube_url = matchingVideoFromNote.url;\n                  resource.video_id = matchingVideoFromNote.id;\n                  resource.from_video_table = true;\n                  console.log(`Updated resource ${resource.id} with YouTube URL from note match:`, matchingVideoFromNote.url);\n                } else {\n                  // If no match in video table, use the URL from the note\n                  resource.youtube_url = resource.youtube_url_from_note;\n                  console.log(`Updated resource ${resource.id} with YouTube URL from note:`, resource.youtube_url_from_note);\n                }\n              }\n            }\n          }\n\n          // If there are more videos than resources, add them as new resources\n          for (const video of videoResult.rows) {\n            // Check if this video is already associated with a resource\n            const existingResource = variable.resources.find(resource =>\n              (resource.trello_card_id && video.trello_card_id &&\n               resource.trello_card_id === video.trello_card_id) ||\n              resource.youtube_url === video.url ||\n              (resource.youtube_url_from_note &&\n               extractYoutubeVideoId(resource.youtube_url_from_note) === extractYoutubeVideoId(video.url))\n            );\n\n            if (!existingResource) {\n              // Add a new resource with this YouTube URL\n              variable.resources.push({\n                id: null, // This will be a virtual resource, not in the src_variable_resources table\n                google_doc_link: '',\n                youtube_url: video.url,\n                note: `YouTube video from Trello (ID: ${video.id})`,\n                account: '',\n                created_at: new Date().toISOString(),\n                video_id: video.id,\n                from_video_table: true // Flag to indicate this came from the video table\n              });\n              console.log(`Added new resource with YouTube URL from video table:`, video.url);\n            }\n          }\n        } else {\n          // If no videos found in the video table, check if any resources have YouTube URLs in their notes\n          for (const resource of variable.resources) {\n            if (resource.youtube_url_from_note && !resource.youtube_url) {\n              resource.youtube_url = resource.youtube_url_from_note;\n              console.log(`Using YouTube URL from note for resource ${resource.id}:`, resource.youtube_url_from_note);\n            }\n          }\n        }\n\n        // Process YouTube URLs for metrics\n        for (const resource of variable.resources) {\n          // Debug: Log each resource's YouTube URL\n          console.log(`Resource ${resource.id} final YouTube URL:`, resource.youtube_url);\n\n          if (resource.youtube_url) {\n            // Add a flag to indicate this is a YouTube resource that needs metrics\n            resource.needs_metrics = true;\n\n            // Extract YouTube video ID from URL (for future reference)\n            try {\n              const url = new URL(resource.youtube_url);\n              const videoId = url.searchParams.get('v');\n              if (videoId) {\n                resource.youtube_id = videoId;\n              }\n            } catch (error) {\n              console.error('Error parsing YouTube URL:', error);\n            }\n          }\n        }\n      }\n\n      // Add variables to the project\n      project.variables = variables;\n\n      return NextResponse.json({ project });\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error fetching project:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch project' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = await params;\n    const projectId = id;\n    const projectData = await request.json();\n\n    // Validate input\n    if (!projectData.name) {\n      return NextResponse.json(\n        { error: 'Project name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Start a transaction\n      await client.query('BEGIN');\n\n      // Check if project exists\n      const projectCheck = await client.query(\n        'SELECT id FROM src_projects WHERE id = $1',\n        [projectId]\n      );\n\n      if (projectCheck.rows.length === 0) {\n        await client.query('ROLLBACK');\n        return NextResponse.json(\n          { error: 'Project not found' },\n          { status: 404 }\n        );\n      }\n\n      // Update project\n      await client.query(\n        `UPDATE src_projects\n         SET name = $1, description = $2, members = $3, created_by = $4\n         WHERE id = $5`,\n        [\n          projectData.name,\n          projectData.description || '',\n          projectData.members || 1,\n          projectData.assignedTo || 'clay', // Update the created_by field with the account value\n          projectId\n        ]\n      );\n\n      // Handle variables\n      if (projectData.variables && Array.isArray(projectData.variables)) {\n        for (const variable of projectData.variables) {\n          if (variable.id) {\n            // Update existing variable\n            await client.query(\n              `UPDATE src_project_variables\n               SET name = $1, note = $2\n               WHERE id = $3 AND project_id = $4`,\n              [variable.name, variable.note || '', variable.id, projectId]\n            );\n          } else if (variable.name) {\n            // Insert new variable\n            const variableResult = await client.query(\n              `INSERT INTO src_project_variables (project_id, name, note)\n               VALUES ($1, $2, $3)\n               RETURNING id`,\n              [projectId, variable.name, variable.note || '']\n            );\n\n            variable.id = variableResult.rows[0].id;\n          }\n\n          // Handle resources for this variable\n          if (variable.id && variable.resources && Array.isArray(variable.resources)) {\n            for (const resource of variable.resources) {\n              // Check if this is a resource from the video table\n              if (resource.from_video_table && resource.video_id) {\n                console.log(`Handling resource from video table: video_id=${resource.video_id}, YouTube URL=${resource.youtubeUrl}`);\n\n                // Update the video table directly\n                if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {\n                  await client.query(\n                    `UPDATE video\n                     SET url = $1\n                     WHERE id = $2`,\n                    [resource.youtubeUrl, resource.video_id]\n                  );\n                  console.log(`Updated video table: ID=${resource.video_id}, YouTube URL=${resource.youtubeUrl}`);\n                }\n\n                // Continue to the next resource\n                continue;\n              }\n\n              if (resource.id) {\n                // Get the current resource data to check if YouTube URL has changed\n                const currentResourceResult = await client.query(\n                  `SELECT youtube_url FROM src_variable_resources WHERE id = $1 AND variable_id = $2`,\n                  [resource.id, variable.id]\n                );\n\n                const oldYoutubeUrl = currentResourceResult.rows.length > 0 ? currentResourceResult.rows[0].youtube_url : '';\n                const newYoutubeUrl = resource.youtubeUrl || '';\n\n                // Update existing resource\n                await client.query(\n                  `UPDATE src_variable_resources\n                   SET google_doc_link = $1, youtube_url = $2, note = $3, account = $4\n                   WHERE id = $5 AND variable_id = $6`,\n                  [\n                    resource.googleDocLink || '',\n                    newYoutubeUrl,\n                    resource.note || '',\n                    resource.account || '',\n                    resource.id,\n                    variable.id\n                  ]\n                );\n\n                // Debug log\n                console.log(`Updated resource in src_variable_resources: ID=${resource.id}, YouTube URL=${newYoutubeUrl}`);\n\n                // If YouTube URL was changed, update the video table\n                if (oldYoutubeUrl !== newYoutubeUrl) {\n                  // Extract video IDs for comparison\n                  const oldVideoId = extractYoutubeVideoId(oldYoutubeUrl);\n                  const newVideoId = extractYoutubeVideoId(newYoutubeUrl);\n\n                  // If the video IDs are the same despite different URLs, no need to update\n                  if (oldVideoId && newVideoId && oldVideoId === newVideoId) {\n                    // URLs are different but point to the same video, just update the URL\n                    await client.query(\n                      `UPDATE video\n                       SET url = $1\n                       WHERE url = $2`,\n                      [newYoutubeUrl, oldYoutubeUrl]\n                    );\n                  } else {\n                    // Different videos, proceed with the update\n\n                    // If old URL exists and is not empty, clear project_id and variable_id\n                    if (oldYoutubeUrl && oldYoutubeUrl.trim() !== '') {\n                      // Get existing writer_id and account_id to preserve them\n                      const oldVideoResult = await client.query(\n                        'SELECT writer_id, account_id FROM video WHERE url LIKE $1',\n                        [`%${oldVideoId}%`]\n                      );\n\n                      if (oldVideoResult.rows.length > 0) {\n                        // Clear project_id and variable_id but preserve writer_id and account_id\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = NULL, variable_id = NULL\n                           WHERE url LIKE $1`,\n                          [`%${oldVideoId}%`]\n                        );\n                      }\n                    }\n\n                    // If new URL exists and is not empty, set project_id and variable_id\n                    if (newYoutubeUrl && newYoutubeUrl.trim() !== '' && newVideoId) {\n                      // Check if this video ID already exists in the video table\n                      const videoCheck = await client.query(\n                        'SELECT id, writer_id, account_id, url FROM video WHERE url LIKE $1',\n                        [`%${newVideoId}%`]\n                      );\n\n                      if (videoCheck.rows.length === 0) {\n                        // Get the account_id from the posting_accounts table\n                        let accountId = null;\n                        try {\n                          const accountResult = await client.query(\n                            `SELECT id FROM posting_accounts WHERE account = $1`,\n                            [resource.account || 'clay']\n                          );\n\n                          if (accountResult.rows.length > 0) {\n                            accountId = accountResult.rows[0].id;\n                          }\n                        } catch (error) {\n                          console.error('Error getting account ID:', error);\n                        }\n\n                        // Insert into video table with project_id and variable_id\n                        await client.query(\n                          `INSERT INTO video (url, account_id, created, writer_id, project_id, variable_id)\n                           VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`,\n                          [\n                            newYoutubeUrl,\n                            accountId || 1,\n                            129, // Default writer_id to 129 for new records\n                            projectId,\n                            variable.id\n                          ]\n                        );\n                      } else {\n                        // Video exists with a different URL format, update with project_id and variable_id\n                        // while preserving writer_id and account_id\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = $1, variable_id = $2\n                           WHERE id = $3`,\n                          [projectId, variable.id, videoCheck.rows[0].id]\n                        );\n                      }\n                    }\n                  }\n                } else if (newYoutubeUrl && newYoutubeUrl.trim() !== '') {\n                  // URL didn't change but make sure project_id and variable_id are set\n                  const videoId = extractYoutubeVideoId(newYoutubeUrl);\n                  if (videoId) {\n                    await client.query(\n                      `UPDATE video\n                       SET project_id = $1, variable_id = $2\n                       WHERE url LIKE $3`,\n                      [projectId, variable.id, `%${videoId}%`]\n                    );\n                  }\n                }\n              } else if (resource.googleDocLink || resource.youtubeUrl) {\n                // Insert new resource\n                await client.query(\n                  `INSERT INTO src_variable_resources (variable_id, google_doc_link, youtube_url, note, account)\n                   VALUES ($1, $2, $3, $4, $5)`,\n                  [\n                    variable.id,\n                    resource.googleDocLink || '',\n                    resource.youtubeUrl || '',\n                    resource.note || '',\n                    resource.account || ''\n                  ]\n                );\n\n                // If it's a YouTube URL, also insert into the video table for the metadata scraper\n                if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {\n                  // Extract the video ID\n                  const videoId = extractYoutubeVideoId(resource.youtubeUrl);\n\n                  if (videoId) {\n                    // Check if this video ID already exists in the video table\n                    const videoCheck = await client.query(\n                      'SELECT id, writer_id, account_id FROM video WHERE url LIKE $1',\n                      [`%${videoId}%`]\n                    );\n\n                    // Get the account_id from the posting_accounts table\n                    let accountId = null;\n                    try {\n                      const accountResult = await client.query(\n                        `SELECT id FROM posting_accounts WHERE account = $1`,\n                        [resource.account || 'clay']\n                      );\n\n                      if (accountResult.rows.length > 0) {\n                        accountId = accountResult.rows[0].id;\n                      }\n                    } catch (error) {\n                      console.error('Error getting account ID:', error);\n                    }\n\n                    if (videoCheck.rows.length === 0) {\n                      // Video doesn't exist, insert it into the video table with the actual variable_id\n                      await client.query(\n                        `INSERT INTO video (url, account_id, created, project_id, variable_id, writer_id)\n                         VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`,\n                        [\n                          resource.youtubeUrl,\n                          accountId || 1,\n                          projectId,\n                          variable.id, // Use the actual variable ID\n                          129 // Always set writer_id to 129\n                        ]\n                      );\n                    } else {\n                      // Video exists with a different URL format, check if it has a writer_id and account_id\n                      const existingWriterId = videoCheck.rows[0].writer_id;\n                      const existingAccountId = videoCheck.rows[0].account_id;\n\n                      if (existingWriterId && existingAccountId) {\n                        // If both writer_id and account_id exist, just update project_id and variable_id\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = $1, variable_id = $2\n                           WHERE id = $3`,\n                          [projectId, variable.id, videoCheck.rows[0].id]\n                        );\n                      } else if (existingWriterId && !existingAccountId) {\n                        // If writer_id exists but no account_id, update with new account_id\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = $1, variable_id = $2, account_id = $3\n                           WHERE id = $4`,\n                          [projectId, variable.id, accountId || 1, videoCheck.rows[0].id]\n                        );\n                      } else if (!existingWriterId && existingAccountId) {\n                        // If account_id exists but no writer_id, update with writer_id = 129\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = $1, variable_id = $2, writer_id = $3\n                           WHERE id = $4`,\n                          [projectId, variable.id, 129, videoCheck.rows[0].id]\n                        );\n                      } else {\n                        // If neither exists, update both\n                        await client.query(\n                          `UPDATE video\n                           SET project_id = $1, variable_id = $2, writer_id = $3, account_id = $4\n                           WHERE id = $5`,\n                          [projectId, variable.id, 129, accountId || 1, videoCheck.rows[0].id]\n                        );\n                      }\n                    }\n                  }\n                }\n\n                // Only create a Trello card if there's a Google Doc link AND no YouTube link\n                if (resource.googleDocLink && resource.googleDocLink.trim() !== '' &&\n                    (!resource.youtubeUrl || resource.youtubeUrl.trim() === '')) {\n                  // Import axios for making HTTP requests from the server\n                  const axios = require('axios');\n\n                  try {\n                    // For new resources, resource.id will be undefined, so we need a different approach\n                    let resourceIndex;\n                    if (resource.id) {\n                      // For existing resources, find the index\n                      resourceIndex = variable.resources.findIndex(r => r.id === resource.id) + 1;\n                    } else {\n                      // For new resources, count how many resources this variable has\n                      const resourceCountResult = await client.query(\n                        `SELECT COUNT(*) FROM src_variable_resources WHERE variable_id = $1`,\n                        [variable.id]\n                      );\n                      resourceIndex = parseInt(resourceCountResult.rows[0].count) + 1;\n                    }\n\n                    // Create a title in the format: {project name}: {variable name}:{resource number}\n                    const cardTitle = `${projectData.name}: ${variable.name}:${resourceIndex}`;\n\n                    // Call the Trello API endpoint directly\n                    const trelloData = {\n                      title: cardTitle,\n                      googleDocLink: resource.googleDocLink,\n                      posting_account: resource.account || 'clay',\n                      variable_id: variable.id,\n                      project_id: projectId\n                    };\n\n                    // Fetch Trello settings\n                    const settingsResult = await client.query(\n                      \"SELECT api_key, token FROM settings ORDER BY id DESC LIMIT 1\"\n                    );\n\n                    if (settingsResult.rows.length === 0) {\n                      console.error('Trello settings not configured');\n                      return;\n                    }\n\n                    const { api_key: apiKey, token: apiToken } = settingsResult.rows[0];\n\n                    // Create the Trello card directly\n                    const autoApprovedListID = \"66982de89e8cb1bfb456ba0a\";\n\n                    // Create the card\n                    const cardResponse = await axios.post(\n                      'https://api.trello.com/1/cards',\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          idList: autoApprovedListID,\n                          name: cardTitle,\n                          desc: `Google Doc Link: ${resource.googleDocLink}`,\n                          pos: 'top'\n                        }\n                      }\n                    );\n\n                    const trelloCardId = cardResponse.data.id;\n\n                    // Add attachment\n                    await axios.post(\n                      `https://api.trello.com/1/cards/${trelloCardId}/attachments`,\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          url: resource.googleDocLink\n                        }\n                      }\n                    );\n\n                    // Discover board and custom-field IDs (for Posting Account)\n                    const cardDetails = await axios.get(\n                      `https://api.trello.com/1/cards/${trelloCardId}`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const boardId = cardDetails.data.idBoard;\n\n                    const customFields = await axios.get(\n                      `https://api.trello.com/1/boards/${boardId}/customFields`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const postingAccountField = customFields.data.find(\n                      f => f.name === \"Posting Account\"\n                    );\n\n                    if (postingAccountField) {\n                      // Set Posting Account custom field\n                      await axios.put(\n                        `https://api.trello.com/1/cards/${trelloCardId}/customField/${postingAccountField.id}/item`,\n                        { value: { text: resource.account || 'clay' } },\n                        { params: { key: apiKey, token: apiToken } }\n                      );\n                    }\n\n                    // Insert into video table with project_id and variable_id\n                    await client.query(\n                      `INSERT INTO video (trello_card_id, variable_id, project_id)\n                       VALUES ($1, $2, $3)`,\n                      [trelloCardId, variable.id, projectId]\n                    );\n\n                    // Insert script into DB with writer_id=129\n                    const insertRes = await client.query(\n                      `INSERT INTO script (\n                         title,\n                         google_doc_link,\n                         approval_status,\n                         created_at,\n                         writer_id,\n                         account_id\n                       )\n                       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5)\n                       RETURNING *`,\n                      [\n                        cardTitle,\n                        resource.googleDocLink,\n                        \"Approved Script. Ready for production\",\n                        129, // Always set writer_id to 129\n                        accountId || 1 // Use the account_id we got earlier or default to 1\n                      ]\n                    );\n\n                    const script = insertRes.rows[0];\n\n                    // Update script with Trello card ID\n                    await client.query(\n                      \"UPDATE script SET trello_card_id = $1 WHERE id = $2\",\n                      [trelloCardId, script.id]\n                    );\n                  } catch (trelloError) {\n                    console.error('Error creating Trello card:', trelloError);\n                    // Continue even if Trello card creation fails\n                  }\n                }\n              }\n            }\n\n            // Handle deleted resources (if deletedResourceIds is provided)\n            if (variable.deletedResourceIds && Array.isArray(variable.deletedResourceIds)) {\n              for (const resourceId of variable.deletedResourceIds) {\n                await client.query(\n                  'DELETE FROM src_variable_resources WHERE id = $1 AND variable_id = $2',\n                  [resourceId, variable.id]\n                );\n              }\n            }\n          }\n        }\n\n        // Handle deleted variables (if deletedVariableIds is provided)\n        if (projectData.deletedVariableIds && Array.isArray(projectData.deletedVariableIds)) {\n          for (const variableId of projectData.deletedVariableIds) {\n            // Delete associated resources first\n            await client.query(\n              'DELETE FROM src_variable_resources WHERE variable_id = $1',\n              [variableId]\n            );\n\n            // Then delete the variable\n            await client.query(\n              'DELETE FROM src_project_variables WHERE id = $1 AND project_id = $2',\n              [variableId, projectId]\n            );\n          }\n        }\n      }\n\n      // Commit the transaction\n      await client.query('COMMIT');\n\n      return NextResponse.json({\n        success: true,\n        message: 'Project updated successfully'\n      });\n    } catch (dbError) {\n      // Rollback in case of error\n      await client.query('ROLLBACK');\n      console.error('Database error:', dbError);\n      throw dbError;\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error updating project:', error);\n    return NextResponse.json(\n      { error: 'Failed to update project' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = await params;\n    const projectId = id;\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Start a transaction\n      await client.query('BEGIN');\n\n      // Check if project exists\n      const projectCheck = await client.query(\n        'SELECT id FROM src_projects WHERE id = $1',\n        [projectId]\n      );\n\n      if (projectCheck.rows.length === 0) {\n        await client.query('ROLLBACK');\n        return NextResponse.json(\n          { error: 'Project not found' },\n          { status: 404 }\n        );\n      }\n\n      // Get all variables for this project\n      const variablesResult = await client.query(\n        'SELECT id FROM src_project_variables WHERE project_id = $1',\n        [projectId]\n      );\n\n      // Delete resources for each variable\n      for (const variable of variablesResult.rows) {\n        await client.query(\n          'DELETE FROM src_variable_resources WHERE variable_id = $1',\n          [variable.id]\n        );\n      }\n\n      // Delete variables\n      await client.query(\n        'DELETE FROM src_project_variables WHERE project_id = $1',\n        [projectId]\n      );\n\n      // Delete project\n      await client.query(\n        'DELETE FROM src_projects WHERE id = $1',\n        [projectId]\n      );\n\n      // Commit the transaction\n      await client.query('COMMIT');\n\n      return NextResponse.json({\n        success: true,\n        message: 'Project deleted successfully'\n      });\n    } catch (dbError) {\n      // Rollback in case of error\n      await client.query('ROLLBACK');\n      console.error('Database error:', dbError);\n      throw dbError;\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error deleting project:', error);\n    return NextResponse.json(\n      { error: 'Failed to delete project' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;AAEA,uEAAuE;AACvE,SAAS,sBAAsB,GAAW;IACxC,IAAI,CAAC,KAAK,OAAO;IAEjB,IAAI;QACF,yBAAyB;QACzB,IAAI,IAAI,QAAQ,CAAC,cAAc;YAC7B,MAAM,KAAK,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,EAAE;YACtD,OAAO,MAAM;QACf;QAEA,mCAAmC;QACnC,IAAI,IAAI,QAAQ,CAAC,aAAa;YAC5B,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,OAAO,CAAC,EAAE;YACrD,OAAO,MAAM;QACf;QAEA,qCAAqC;QACrC,IAAI,IAAI,QAAQ,CAAC,YAAY;YAC3B,MAAM,SAAS,IAAI,IAAI;YACvB,OAAO,OAAO,YAAY,CAAC,GAAG,CAAC;QACjC;QAEA,iCAAiC;QACjC,MAAM,QAAQ;QACd,MAAM,QAAQ,IAAI,KAAK,CAAC;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;IACT;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,2CAA2C;QAC3C,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,YAAY;QAElB,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,qCAAqC;YACrC,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC;;sBAEa,CAAC,EACf;gBAAC;aAAU;YAGb,IAAI,cAAc,IAAI,CAAC,MAAM,KAAK,GAAG;gBACnC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,UAAU,cAAc,IAAI,CAAC,EAAE;YAErC,+CAA+C;YAC/C,MAAM,kBAAkB,MAAM,OAAO,KAAK,CACxC,CAAC;;;4BAGmB,CAAC,EACrB;gBAAC;aAAU;YAGb,MAAM,YAAY,gBAAgB,IAAI;YAEtC,uCAAuC;YACvC,KAAK,MAAM,YAAY,UAAW;gBAChC,yDAAyD;gBACzD,MAAM,kBAAkB,MAAM,OAAO,KAAK,CACxC,CAAC;;;8BAGmB,CAAC,EACrB;oBAAC,SAAS,EAAE;iBAAC;gBAGf,6CAA6C;gBAC7C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,8BAA8B,CAAC,EAAE,gBAAgB,IAAI;gBAEzH,SAAS,SAAS,GAAG,gBAAgB,IAAI;gBAEzC,yFAAyF;gBACzF,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;oBACzC,IAAI,SAAS,eAAe,EAAE;wBAC5B,IAAI;4BACF,0EAA0E;4BAC1E,MAAM,eAAe,MAAM,OAAO,KAAK,CACrC,CAAC;;2CAE0B,CAAC,EAC5B;gCAAC,SAAS,eAAe;6BAAC;4BAG5B,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,SAAS,EAAE,CAAC,sBAAsB,EAAE,SAAS,eAAe,CAAC,CAAC,CAAC,EAAE,aAAa,IAAI;4BAE7H,qEAAqE;4BACrE,IAAI,aAAa,IAAI,CAAC,MAAM,GAAG,GAAG;gCAChC,SAAS,cAAc,GAAG,aAAa,IAAI,CAAC,EAAE,CAAC,cAAc;gCAC7D,SAAS,SAAS,GAAG,aAAa,IAAI,CAAC,EAAE,CAAC,EAAE;gCAC5C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,SAAS,cAAc,CAAC,aAAa,EAAE,SAAS,EAAE,EAAE;4BAC1F;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,SAAS,eAAe,CAAC,CAAC,CAAC,EAAE;wBAChG;wBAEA,wDAAwD;wBACxD,IAAI,SAAS,IAAI,IAAI,OAAO,SAAS,IAAI,KAAK,UAAU;4BACtD,2CAA2C;4BAC3C,MAAM,kBAAkB,SAAS,IAAI,CAAC,KAAK,CAAC;4BAC5C,IAAI,iBAAiB;gCACnB,MAAM,aAAa,eAAe,CAAC,EAAE;gCACrC,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,SAAS,EAAE,CAAC,EAAE,EAAE,YAAY;gCAClF,SAAS,qBAAqB,GAAG;4BACnC;wBACF;oBACF;gBACF;gBAEA,gFAAgF;gBAChF,MAAM,cAAc,MAAM,OAAO,KAAK,CACpC,CAAC;;mEAEwD,CAAC,EAC1D;oBAAC,SAAS,EAAE;iBAAC;gBAGf,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,IAAI;gBAE1F,sEAAsE;gBACtE,MAAM,gBAAgB,SAAS,SAAS,CACrC,MAAM,CAAC,CAAA,WAAY,SAAS,cAAc,EAC1C,GAAG,CAAC,CAAA,WAAY,SAAS,cAAc;gBAE1C,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,MAAM,oBAAoB,MAAM,OAAO,KAAK,CAC1C,CAAC;;6EAEgE,CAAC,EAClE;wBAAC;qBAAc;oBAGjB,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC,EAAE,kBAAkB,IAAI;oBAExF,mEAAmE;oBACnE,KAAK,MAAM,SAAS,kBAAkB,IAAI,CAAE;wBAC1C,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,MAAM,EAAE,GAAG;4BAClD,YAAY,IAAI,CAAC,IAAI,CAAC;wBACxB;oBACF;gBACF;gBAEA,oEAAoE;gBACpE,IAAI,YAAY,IAAI,CAAC,MAAM,GAAG,GAAG;oBAC/B,gFAAgF;oBAChF,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;wBACzC,8DAA8D;wBAC9D,MAAM,gBAAgB,YAAY,IAAI,CAAC,IAAI,CAAC,CAAA,QACzC,SAAS,cAAc,IAAI,MAAM,cAAc,IAC/C,SAAS,cAAc,KAAK,MAAM,cAAc;wBAGnD,IAAI,eAAe;4BACjB,gEAAgE;4BAChE,SAAS,WAAW,GAAG,cAAc,GAAG;4BACxC,SAAS,QAAQ,GAAG,cAAc,EAAE;4BACpC,SAAS,gBAAgB,GAAG;4BAC5B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,mCAAmC,CAAC,EAAE,cAAc,GAAG;wBACrG,OAAO,IAAI,SAAS,qBAAqB,EAAE;4BACzC,yEAAyE;4BACzE,MAAM,kBAAkB,sBAAsB,SAAS,qBAAqB;4BAC5E,IAAI,iBAAiB;gCACnB,MAAM,wBAAwB,YAAY,IAAI,CAAC,IAAI,CAAC,CAAA;oCAClD,MAAM,UAAU,sBAAsB,MAAM,GAAG;oCAC/C,OAAO,YAAY;gCACrB;gCAEA,IAAI,uBAAuB;oCACzB,SAAS,WAAW,GAAG,sBAAsB,GAAG;oCAChD,SAAS,QAAQ,GAAG,sBAAsB,EAAE;oCAC5C,SAAS,gBAAgB,GAAG;oCAC5B,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,kCAAkC,CAAC,EAAE,sBAAsB,GAAG;gCAC5G,OAAO;oCACL,wDAAwD;oCACxD,SAAS,WAAW,GAAG,SAAS,qBAAqB;oCACrD,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,SAAS,EAAE,CAAC,4BAA4B,CAAC,EAAE,SAAS,qBAAqB;gCAC3G;4BACF;wBACF;oBACF;oBAEA,qEAAqE;oBACrE,KAAK,MAAM,SAAS,YAAY,IAAI,CAAE;wBACpC,4DAA4D;wBAC5D,MAAM,mBAAmB,SAAS,SAAS,CAAC,IAAI,CAAC,CAAA,WAC/C,AAAC,SAAS,cAAc,IAAI,MAAM,cAAc,IAC/C,SAAS,cAAc,KAAK,MAAM,cAAc,IACjD,SAAS,WAAW,KAAK,MAAM,GAAG,IACjC,SAAS,qBAAqB,IAC9B,sBAAsB,SAAS,qBAAqB,MAAM,sBAAsB,MAAM,GAAG;wBAG5F,IAAI,CAAC,kBAAkB;4BACrB,2CAA2C;4BAC3C,SAAS,SAAS,CAAC,IAAI,CAAC;gCACtB,IAAI;gCACJ,iBAAiB;gCACjB,aAAa,MAAM,GAAG;gCACtB,MAAM,CAAC,+BAA+B,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;gCACnD,SAAS;gCACT,YAAY,IAAI,OAAO,WAAW;gCAClC,UAAU,MAAM,EAAE;gCAClB,kBAAkB,KAAK,kDAAkD;4BAC3E;4BACA,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC,EAAE,MAAM,GAAG;wBAChF;oBACF;gBACF,OAAO;oBACL,iGAAiG;oBACjG,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;wBACzC,IAAI,SAAS,qBAAqB,IAAI,CAAC,SAAS,WAAW,EAAE;4BAC3D,SAAS,WAAW,GAAG,SAAS,qBAAqB;4BACrD,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC,EAAE,SAAS,qBAAqB;wBACxG;oBACF;gBACF;gBAEA,mCAAmC;gBACnC,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;oBACzC,yCAAyC;oBACzC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,CAAC,mBAAmB,CAAC,EAAE,SAAS,WAAW;oBAE9E,IAAI,SAAS,WAAW,EAAE;wBACxB,uEAAuE;wBACvE,SAAS,aAAa,GAAG;wBAEzB,2DAA2D;wBAC3D,IAAI;4BACF,MAAM,MAAM,IAAI,IAAI,SAAS,WAAW;4BACxC,MAAM,UAAU,IAAI,YAAY,CAAC,GAAG,CAAC;4BACrC,IAAI,SAAS;gCACX,SAAS,UAAU,GAAG;4BACxB;wBACF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,8BAA8B;wBAC9C;oBACF;gBACF;YACF;YAEA,+BAA+B;YAC/B,QAAQ,SAAS,GAAG;YAEpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAQ;QACrC,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,YAAY;QAClB,MAAM,cAAc,MAAM,QAAQ,IAAI;QAEtC,iBAAiB;QACjB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,sBAAsB;YACtB,MAAM,OAAO,KAAK,CAAC;YAEnB,0BAA0B;YAC1B,MAAM,eAAe,MAAM,OAAO,KAAK,CACrC,6CACA;gBAAC;aAAU;YAGb,IAAI,aAAa,IAAI,CAAC,MAAM,KAAK,GAAG;gBAClC,MAAM,OAAO,KAAK,CAAC;gBACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,iBAAiB;YACjB,MAAM,OAAO,KAAK,CAChB,CAAC;;sBAEa,CAAC,EACf;gBACE,YAAY,IAAI;gBAChB,YAAY,WAAW,IAAI;gBAC3B,YAAY,OAAO,IAAI;gBACvB,YAAY,UAAU,IAAI;gBAC1B;aACD;YAGH,mBAAmB;YACnB,IAAI,YAAY,SAAS,IAAI,MAAM,OAAO,CAAC,YAAY,SAAS,GAAG;gBACjE,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;oBAC5C,IAAI,SAAS,EAAE,EAAE;wBACf,2BAA2B;wBAC3B,MAAM,OAAO,KAAK,CAChB,CAAC;;gDAEiC,CAAC,EACnC;4BAAC,SAAS,IAAI;4BAAE,SAAS,IAAI,IAAI;4BAAI,SAAS,EAAE;4BAAE;yBAAU;oBAEhE,OAAO,IAAI,SAAS,IAAI,EAAE;wBACxB,sBAAsB;wBACtB,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC,CAAC;;2BAEY,CAAC,EACd;4BAAC;4BAAW,SAAS,IAAI;4BAAE,SAAS,IAAI,IAAI;yBAAG;wBAGjD,SAAS,EAAE,GAAG,eAAe,IAAI,CAAC,EAAE,CAAC,EAAE;oBACzC;oBAEA,qCAAqC;oBACrC,IAAI,SAAS,EAAE,IAAI,SAAS,SAAS,IAAI,MAAM,OAAO,CAAC,SAAS,SAAS,GAAG;wBAC1E,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;4BACzC,mDAAmD;4BACnD,IAAI,SAAS,gBAAgB,IAAI,SAAS,QAAQ,EAAE;gCAClD,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,SAAS,QAAQ,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;gCAEnH,kCAAkC;gCAClC,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,IAAI;oCAC5D,MAAM,OAAO,KAAK,CAChB,CAAC;;kCAEa,CAAC,EACf;wCAAC,SAAS,UAAU;wCAAE,SAAS,QAAQ;qCAAC;oCAE1C,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,SAAS,QAAQ,CAAC,cAAc,EAAE,SAAS,UAAU,EAAE;gCAChG;gCAGA;4BACF;4BAEA,IAAI,SAAS,EAAE,EAAE;gCACf,oEAAoE;gCACpE,MAAM,wBAAwB,MAAM,OAAO,KAAK,CAC9C,CAAC,iFAAiF,CAAC,EACnF;oCAAC,SAAS,EAAE;oCAAE,SAAS,EAAE;iCAAC;gCAG5B,MAAM,gBAAgB,sBAAsB,IAAI,CAAC,MAAM,GAAG,IAAI,sBAAsB,IAAI,CAAC,EAAE,CAAC,WAAW,GAAG;gCAC1G,MAAM,gBAAgB,SAAS,UAAU,IAAI;gCAE7C,2BAA2B;gCAC3B,MAAM,OAAO,KAAK,CAChB,CAAC;;qDAEkC,CAAC,EACpC;oCACE,SAAS,aAAa,IAAI;oCAC1B;oCACA,SAAS,IAAI,IAAI;oCACjB,SAAS,OAAO,IAAI;oCACpB,SAAS,EAAE;oCACX,SAAS,EAAE;iCACZ;gCAGH,YAAY;gCACZ,QAAQ,GAAG,CAAC,CAAC,+CAA+C,EAAE,SAAS,EAAE,CAAC,cAAc,EAAE,eAAe;gCAEzG,qDAAqD;gCACrD,IAAI,kBAAkB,eAAe;oCACnC,mCAAmC;oCACnC,MAAM,aAAa,sBAAsB;oCACzC,MAAM,aAAa,sBAAsB;oCAEzC,0EAA0E;oCAC1E,IAAI,cAAc,cAAc,eAAe,YAAY;wCACzD,sEAAsE;wCACtE,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAe;yCAAc;oCAElC,OAAO;wCACL,4CAA4C;wCAE5C,uEAAuE;wCACvE,IAAI,iBAAiB,cAAc,IAAI,OAAO,IAAI;4CAChD,yDAAyD;4CACzD,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC,6DACA;gDAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;6CAAC;4CAGrB,IAAI,eAAe,IAAI,CAAC,MAAM,GAAG,GAAG;gDAClC,yEAAyE;gDACzE,MAAM,OAAO,KAAK,CAChB,CAAC;;4CAEiB,CAAC,EACnB;oDAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;iDAAC;4CAEvB;wCACF;wCAEA,qEAAqE;wCACrE,IAAI,iBAAiB,cAAc,IAAI,OAAO,MAAM,YAAY;4CAC9D,2DAA2D;4CAC3D,MAAM,aAAa,MAAM,OAAO,KAAK,CACnC,sEACA;gDAAC,CAAC,CAAC,EAAE,WAAW,CAAC,CAAC;6CAAC;4CAGrB,IAAI,WAAW,IAAI,CAAC,MAAM,KAAK,GAAG;gDAChC,qDAAqD;gDACrD,IAAI,aAAY;gDAChB,IAAI;oDACF,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC,kDAAkD,CAAC,EACpD;wDAAC,SAAS,OAAO,IAAI;qDAAO;oDAG9B,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,GAAG;wDACjC,aAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;oDACtC;gDACF,EAAE,OAAO,OAAO;oDACd,QAAQ,KAAK,CAAC,6BAA6B;gDAC7C;gDAEA,0DAA0D;gDAC1D,MAAM,OAAO,KAAK,CAChB,CAAC;yEAC8C,CAAC,EAChD;oDACE;oDACA,cAAa;oDACb;oDACA;oDACA,SAAS,EAAE;iDACZ;4CAEL,OAAO;gDACL,mFAAmF;gDACnF,4CAA4C;gDAC5C,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEa,CAAC,EACf;oDAAC;oDAAW,SAAS,EAAE;oDAAE,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE;iDAAC;4CAEnD;wCACF;oCACF;gCACF,OAAO,IAAI,iBAAiB,cAAc,IAAI,OAAO,IAAI;oCACvD,qEAAqE;oCACrE,MAAM,UAAU,sBAAsB;oCACtC,IAAI,SAAS;wCACX,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEiB,CAAC,EACnB;4CAAC;4CAAW,SAAS,EAAE;4CAAE,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;yCAAC;oCAE5C;gCACF;4BACF,OAAO,IAAI,SAAS,aAAa,IAAI,SAAS,UAAU,EAAE;gCACxD,sBAAsB;gCACtB,MAAM,OAAO,KAAK,CAChB,CAAC;8CAC2B,CAAC,EAC7B;oCACE,SAAS,EAAE;oCACX,SAAS,aAAa,IAAI;oCAC1B,SAAS,UAAU,IAAI;oCACvB,SAAS,IAAI,IAAI;oCACjB,SAAS,OAAO,IAAI;iCACrB;gCAGH,mFAAmF;gCACnF,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,IAAI;oCAC5D,uBAAuB;oCACvB,MAAM,UAAU,sBAAsB,SAAS,UAAU;oCAEzD,IAAI,SAAS;wCACX,2DAA2D;wCAC3D,MAAM,aAAa,MAAM,OAAO,KAAK,CACnC,iEACA;4CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;yCAAC;wCAGlB,qDAAqD;wCACrD,IAAI,aAAY;wCAChB,IAAI;4CACF,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC,kDAAkD,CAAC,EACpD;gDAAC,SAAS,OAAO,IAAI;6CAAO;4CAG9B,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,GAAG;gDACjC,aAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;4CACtC;wCACF,EAAE,OAAO,OAAO;4CACd,QAAQ,KAAK,CAAC,6BAA6B;wCAC7C;wCAEA,IAAI,WAAW,IAAI,CAAC,MAAM,KAAK,GAAG;4CAChC,kFAAkF;4CAClF,MAAM,OAAO,KAAK,CAChB,CAAC;uEAC8C,CAAC,EAChD;gDACE,SAAS,UAAU;gDACnB,cAAa;gDACb;gDACA,SAAS,EAAE;gDACX,IAAI,8BAA8B;6CACnC;wCAEL,OAAO;4CACL,uFAAuF;4CACvF,MAAM,mBAAmB,WAAW,IAAI,CAAC,EAAE,CAAC,SAAS;4CACrD,MAAM,oBAAoB,WAAW,IAAI,CAAC,EAAE,CAAC,UAAU;4CAEvD,IAAI,oBAAoB,mBAAmB;gDACzC,iFAAiF;gDACjF,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEa,CAAC,EACf;oDAAC;oDAAW,SAAS,EAAE;oDAAE,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE;iDAAC;4CAEnD,OAAO,IAAI,oBAAoB,CAAC,mBAAmB;gDACjD,oEAAoE;gDACpE,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEa,CAAC,EACf;oDAAC;oDAAW,SAAS,EAAE;oDAAE,cAAa;oDAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE;iDAAC;4CAEnE,OAAO,IAAI,CAAC,oBAAoB,mBAAmB;gDACjD,qEAAqE;gDACrE,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEa,CAAC,EACf;oDAAC;oDAAW,SAAS,EAAE;oDAAE;oDAAK,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE;iDAAC;4CAExD,OAAO;gDACL,iCAAiC;gDACjC,MAAM,OAAO,KAAK,CAChB,CAAC;;wCAEa,CAAC,EACf;oDAAC;oDAAW,SAAS,EAAE;oDAAE;oDAAK,cAAa;oDAAG,WAAW,IAAI,CAAC,EAAE,CAAC,EAAE;iDAAC;4CAExE;wCACF;oCACF;gCACF;gCAEA,6EAA6E;gCAC7E,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,IAAI,OAAO,MAC5D,CAAC,CAAC,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,EAAE,GAAG;oCAC/D,wDAAwD;oCACxD,MAAM;oCAEN,IAAI;wCACF,oFAAoF;wCACpF,IAAI;wCACJ,IAAI,SAAS,EAAE,EAAE;4CACf,yCAAyC;4CACzC,gBAAgB,SAAS,SAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE,IAAI;wCAC5E,OAAO;4CACL,gEAAgE;4CAChE,MAAM,sBAAsB,MAAM,OAAO,KAAK,CAC5C,CAAC,kEAAkE,CAAC,EACpE;gDAAC,SAAS,EAAE;6CAAC;4CAEf,gBAAgB,SAAS,oBAAoB,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI;wCAChE;wCAEA,kFAAkF;wCAClF,MAAM,YAAY,GAAG,YAAY,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,eAAe;wCAE1E,wCAAwC;wCACxC,MAAM,aAAa;4CACjB,OAAO;4CACP,eAAe,SAAS,aAAa;4CACrC,iBAAiB,SAAS,OAAO,IAAI;4CACrC,aAAa,SAAS,EAAE;4CACxB,YAAY;wCACd;wCAEA,wBAAwB;wCACxB,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC;wCAGF,IAAI,eAAe,IAAI,CAAC,MAAM,KAAK,GAAG;4CACpC,QAAQ,KAAK,CAAC;4CACd;wCACF;wCAEA,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,eAAe,IAAI,CAAC,EAAE;wCAEnE,kCAAkC;wCAClC,MAAM,qBAAqB;wCAE3B,kBAAkB;wCAClB,MAAM,eAAe,MAAM,MAAM,IAAI,CACnC,kCACA,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,MAAM;gDACN,MAAM,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE;gDAClD,KAAK;4CACP;wCACF;wCAGF,MAAM,eAAe,aAAa,IAAI,CAAC,EAAE;wCAEzC,iBAAiB;wCACjB,MAAM,MAAM,IAAI,CACd,CAAC,+BAA+B,EAAE,aAAa,YAAY,CAAC,EAC5D,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,KAAK,SAAS,aAAa;4CAC7B;wCACF;wCAGF,4DAA4D;wCAC5D,MAAM,cAAc,MAAM,MAAM,GAAG,CACjC,CAAC,+BAA+B,EAAE,cAAc,EAChD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,UAAU,YAAY,IAAI,CAAC,OAAO;wCAExC,MAAM,eAAe,MAAM,MAAM,GAAG,CAClC,CAAC,gCAAgC,EAAE,QAAQ,aAAa,CAAC,EACzD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,sBAAsB,aAAa,IAAI,CAAC,IAAI,CAChD,CAAA,IAAK,EAAE,IAAI,KAAK;wCAGlB,IAAI,qBAAqB;4CACvB,mCAAmC;4CACnC,MAAM,MAAM,GAAG,CACb,CAAC,+BAA+B,EAAE,aAAa,aAAa,EAAE,oBAAoB,EAAE,CAAC,KAAK,CAAC,EAC3F;gDAAE,OAAO;oDAAE,MAAM,SAAS,OAAO,IAAI;gDAAO;4CAAE,GAC9C;gDAAE,QAAQ;oDAAE,KAAK;oDAAQ,OAAO;gDAAS;4CAAE;wCAE/C;wCAEA,0DAA0D;wCAC1D,MAAM,OAAO,KAAK,CAChB,CAAC;0CACmB,CAAC,EACrB;4CAAC;4CAAc,SAAS,EAAE;4CAAE;yCAAU;wCAGxC,2CAA2C;wCAC3C,MAAM,YAAY,MAAM,OAAO,KAAK,CAClC,CAAC;;;;;;;;;kCASW,CAAC,EACb;4CACE;4CACA,SAAS,aAAa;4CACtB;4CACA;4CACA,aAAa,EAAE,oDAAoD;yCACpE;wCAGH,MAAM,SAAS,UAAU,IAAI,CAAC,EAAE;wCAEhC,oCAAoC;wCACpC,MAAM,OAAO,KAAK,CAChB,uDACA;4CAAC;4CAAc,OAAO,EAAE;yCAAC;oCAE7B,EAAE,OAAO,aAAa;wCACpB,QAAQ,KAAK,CAAC,+BAA+B;oCAC7C,8CAA8C;oCAChD;gCACF;4BACF;wBACF;wBAEA,+DAA+D;wBAC/D,IAAI,SAAS,kBAAkB,IAAI,MAAM,OAAO,CAAC,SAAS,kBAAkB,GAAG;4BAC7E,KAAK,MAAM,cAAc,SAAS,kBAAkB,CAAE;gCACpD,MAAM,OAAO,KAAK,CAChB,yEACA;oCAAC;oCAAY,SAAS,EAAE;iCAAC;4BAE7B;wBACF;oBACF;gBACF;gBAEA,+DAA+D;gBAC/D,IAAI,YAAY,kBAAkB,IAAI,MAAM,OAAO,CAAC,YAAY,kBAAkB,GAAG;oBACnF,KAAK,MAAM,cAAc,YAAY,kBAAkB,CAAE;wBACvD,oCAAoC;wBACpC,MAAM,OAAO,KAAK,CAChB,6DACA;4BAAC;yBAAW;wBAGd,2BAA2B;wBAC3B,MAAM,OAAO,KAAK,CAChB,uEACA;4BAAC;4BAAY;yBAAU;oBAE3B;gBACF;YACF;YAEA,yBAAyB;YACzB,MAAM,OAAO,KAAK,CAAC;YAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,SAAS;YAChB,4BAA4B;YAC5B,MAAM,OAAO,KAAK,CAAC;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,YAAY;QAElB,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,sBAAsB;YACtB,MAAM,OAAO,KAAK,CAAC;YAEnB,0BAA0B;YAC1B,MAAM,eAAe,MAAM,OAAO,KAAK,CACrC,6CACA;gBAAC;aAAU;YAGb,IAAI,aAAa,IAAI,CAAC,MAAM,KAAK,GAAG;gBAClC,MAAM,OAAO,KAAK,CAAC;gBACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,qCAAqC;YACrC,MAAM,kBAAkB,MAAM,OAAO,KAAK,CACxC,8DACA;gBAAC;aAAU;YAGb,qCAAqC;YACrC,KAAK,MAAM,YAAY,gBAAgB,IAAI,CAAE;gBAC3C,MAAM,OAAO,KAAK,CAChB,6DACA;oBAAC,SAAS,EAAE;iBAAC;YAEjB;YAEA,mBAAmB;YACnB,MAAM,OAAO,KAAK,CAChB,2DACA;gBAAC;aAAU;YAGb,iBAAiB;YACjB,MAAM,OAAO,KAAK,CAChB,0CACA;gBAAC;aAAU;YAGb,yBAAyB;YACzB,MAAM,OAAO,KAAK,CAAC;YAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF,EAAE,OAAO,SAAS;YAChB,4BAA4B;YAC5B,MAAM,OAAO,KAAK,CAAC;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}