const axios = require('axios');

async function testPostingAccountAPI() {
  const trelloCardId = '68341ea2b3a3fb16d5768c35';

  console.log('Testing getPostingAccount API...');
  console.log('Trello Card ID:', trelloCardId);
  console.log('URL:', 'http://my-app-heqr6b54qa-uc.a.run.app/api/getPostingAccount');

  try {
    // Test with correct POST method (as the endpoint expects)
    console.log('\n--- Testing POST method with correct body ---');
    const postResponse = await axios.post(
      'http://my-app-heqr6b54qa-uc.a.run.app/api/getPostingAccount',
      {
        trello_card_id: trelloCardId,
        ignore_daily_limit: true
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ POST Status:', postResponse.status);
    console.log('✅ POST Headers:', postResponse.headers);
    console.log('✅ POST Data:', postResponse.data);

    if (postResponse.data && postResponse.data.success && postResponse.data.account) {
      console.log('🎉 SUCCESS: Got posting account:', postResponse.data.account);
      console.log('🎉 Ignored daily limit:', postResponse.data.ignored_daily_limit);
    } else {
      console.log('❌ ISSUE: No account in response or request failed');
      console.log('Response details:', postResponse.data);
    }

  } catch (postError) {
    console.error('❌ POST ERROR:', postError.message);
    if (postError.response) {
      console.log('Error Status:', postError.response.status);
      console.log('Error Data:', postError.response.data);
    }
  }

  try {
    // Test without ignore_daily_limit to see normal behavior
    console.log('\n--- Testing POST method without ignore_daily_limit ---');
    const normalResponse = await axios.post(
      'http://my-app-heqr6b54qa-uc.a.run.app/api/getPostingAccount',
      {
        trello_card_id: trelloCardId
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ Normal Status:', normalResponse.status);
    console.log('✅ Normal Data:', normalResponse.data);

  } catch (normalError) {
    console.error('❌ Normal request ERROR:', normalError.message);
    if (normalError.response) {
      console.log('Normal Error Status:', normalError.response.status);
      console.log('Normal Error Data:', normalError.response.data);
    }
  }
}

testPostingAccountAPI();
