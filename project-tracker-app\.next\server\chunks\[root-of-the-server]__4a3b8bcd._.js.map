{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/api/ab-projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { Pool } from 'pg';\nimport axios from 'axios';\n\n// Reuse the existing pool configuration\nconst getPoolConfig = () => {\n  const config = {\n    host: '***********',\n    database: process.env.DB_NAME || 'postgres',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASS || 'Plotpointe!@3456',\n    port: 5432,\n    ssl: { rejectUnauthorized: false }\n  };\n\n  return config;\n};\n\n// Create the connection pool\nconst pool = new Pool(getPoolConfig());\n\n// Add error handler\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\n// AB Testing specific Trello list ID\nconst abAutoApprovedListID = \"682c292de90489812322e7ea\"; // Replace with your AB testing list ID\n\nexport async function POST(request: NextRequest) {\n  try {\n    const projectData = await request.json();\n\n    // Validate input\n    if (!projectData.name) {\n      return NextResponse.json(\n        { error: 'Project name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Start a transaction\n      await client.query('BEGIN');\n\n      // Insert project into AB table\n      const projectResult = await client.query(\n        `INSERT INTO ab_src_projects (name, description, members)\n         VALUES ($1, $2, $3)\n         RETURNING id`,\n        [\n          projectData.name,\n          projectData.description || '',\n          1 // Default members count\n        ]\n      );\n\n      const projectId = projectResult.rows[0].id;\n\n      // Insert variables and resources\n      if (projectData.variables && projectData.variables.length > 0) {\n        for (const variable of projectData.variables) {\n          if (!variable.name) continue;\n\n          const variableResult = await client.query(\n            `INSERT INTO ab_src_project_variables (project_id, name, note)\n             VALUES ($1, $2, $3)\n             RETURNING id`,\n            [projectId, variable.name, variable.note || '']\n          );\n\n          const variableId = variableResult.rows[0].id;\n\n          // Insert resources\n          if (variable.resources && variable.resources.length > 0) {\n            for (const resource of variable.resources) {\n              if (!resource.googleDocLink && !resource.youtubeUrl) continue;\n\n              // Insert into ab_src_variable_resources\n              await client.query(\n                `INSERT INTO ab_src_variable_resources (variable_id, google_doc_link, youtube_url, note, account)\n                 VALUES ($1, $2, $3, $4, $5)\n                 RETURNING id`,\n                [\n                  variableId,\n                  resource.googleDocLink || '',\n                  resource.youtubeUrl || '',\n                  resource.note || '',\n                  resource.account || ''\n                ]\n              );\n\n              // Get the account_id from the posting_accounts table (reuse existing table)\n              let accountId = null;\n\n              try {\n                // Only query for account_id if resource.account is provided and not 'Auto Assign'\n                if (resource.account && resource.account !== 'Auto Assign') {\n                  const accountResult = await client.query(\n                    `SELECT id FROM posting_accounts WHERE account = $1`,\n                    [resource.account]\n                  );\n\n                  if (accountResult.rows.length > 0) {\n                    accountId = accountResult.rows[0].id;\n                  }\n                }\n              } catch (error) {\n                console.error('Error getting account ID:', error);\n                // Continue without account_id if there's an error\n              }\n\n              // If it's a YouTube URL, check if it already exists in the ab_video table\n              if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {\n                // Check if the YouTube URL already exists in the ab_video table\n                const videoCheckResult = await client.query(\n                  `SELECT id, writer_id, account_id FROM ab_video WHERE url = $1`,\n                  [resource.youtubeUrl]\n                );\n\n                if (videoCheckResult.rows.length > 0) {\n                  // URL exists, check if it has a writer_id and account_id\n                  const existingWriterId = videoCheckResult.rows[0].writer_id;\n                  const existingAccountId = videoCheckResult.rows[0].account_id;\n\n                  if (existingWriterId && existingAccountId) {\n                    // If both writer_id and account_id exist, just update project_id and variable_id\n                    await client.query(\n                      `UPDATE ab_video\n                       SET project_id = $1, variable_id = $2\n                       WHERE url = $3`,\n                      [projectId, variableId, resource.youtubeUrl]\n                    );\n                  } else if (existingWriterId && !existingAccountId) {\n                    // If writer_id exists but no account_id, update with new account_id\n                    await client.query(\n                      `UPDATE ab_video\n                       SET project_id = $1, variable_id = $2, account_id = $3\n                       WHERE url = $4`,\n                      [projectId, variableId, accountId, resource.youtubeUrl]\n                    );\n                  } else if (!existingWriterId && existingAccountId) {\n                    // If account_id exists but no writer_id, update with writer_id = 129\n                    await client.query(\n                      `UPDATE ab_video\n                       SET project_id = $1, variable_id = $2, writer_id = $3\n                       WHERE url = $4`,\n                      [projectId, variableId, 129, resource.youtubeUrl]\n                    );\n                  } else {\n                    // If neither exists, update both\n                    await client.query(\n                      `UPDATE ab_video\n                       SET project_id = $1, variable_id = $2, writer_id = $3, account_id = $4\n                       WHERE url = $5`,\n                      [projectId, variableId, 129, accountId, resource.youtubeUrl]\n                    );\n                  }\n                } else {\n                  // URL doesn't exist, insert it into the ab_video table with the actual variable_id\n                  await client.query(\n                    `INSERT INTO ab_video (url, account_id, created, project_id, variable_id, writer_id)\n                     VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`,\n                    [\n                      resource.youtubeUrl,\n                      accountId, // Use the account_id we determined, or null if not found/Auto Assign\n                      projectId,\n                      variableId, // Use the actual variable ID\n                      129 // Always set writer_id to 129\n                    ]\n                  );\n                }\n              }\n\n              // Only create a Trello card if there's a Google Doc link AND no YouTube link\n              if (resource.googleDocLink && resource.googleDocLink.trim() !== '' &&\n                  (!resource.youtubeUrl || resource.youtubeUrl.trim() === '')) {\n                try {\n                  // Get the resource index within this variable\n                  const resourceIndex = variable.resources.indexOf(resource) + 1;\n\n                  // Create a title in the format: {project name}: {variable name}:{resource number}\n                  const cardTitle = `AB-${projectData.name}: ${variable.name}:${resourceIndex}`;\n\n                  try {\n                    // Fetch Trello settings (reuse existing table)\n                    const settingsResult = await client.query(\n                      \"SELECT api_key, token FROM settings ORDER BY id DESC LIMIT 1\"\n                    );\n\n                    if (settingsResult.rows.length === 0) {\n                      console.error('Trello settings not configured');\n                      return;\n                    }\n\n                    const { api_key: apiKey, token: apiToken } = settingsResult.rows[0];\n\n                    // Create the Trello card directly using the AB list ID\n                    const cardResponse = await axios.post(\n                      'https://api.trello.com/1/cards',\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          idList: abAutoApprovedListID, // Use AB-specific list ID\n                          name: cardTitle,\n                          desc: `Google Doc Link: ${resource.googleDocLink}`,\n                          pos: 'top'\n                        }\n                      }\n                    );\n\n                    const trelloCardId = cardResponse.data.id;\n\n                    // Add attachment\n                    await axios.post(\n                      `https://api.trello.com/1/cards/${trelloCardId}/attachments`,\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          url: resource.googleDocLink\n                        }\n                      }\n                    );\n\n                    // Discover board and custom-field IDs (for Posting Account)\n                    const cardDetails = await axios.get(\n                      `https://api.trello.com/1/cards/${trelloCardId}`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const boardId = cardDetails.data.idBoard;\n\n                    const customFields = await axios.get(\n                      `https://api.trello.com/1/boards/${boardId}/customFields`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const postingAccountField = customFields.data.find(\n                      f => f.name === \"Posting Account\"\n                    );\n\n                    let postingAccount = resource.account;\n\n                    // Check if posting account is 'Auto Assign'\n                    if (postingAccount === 'Auto Assign') {\n                      try {\n                        // Call the API to get a posting account\n                        const postingAccountResponse = await axios.get(\n                          'http://my-app-heqr6b54qa-uc.a.run.app/api/getPostingAccount',\n                          {\n                            params: {\n                              trello_card_id: trelloCardId,\n                              ignore_daily_limit: true\n                            }\n                          }\n                        );\n\n                        // If we got a valid response, use the returned posting account\n                        if (postingAccountResponse.data && postingAccountResponse.data.account) {\n                          postingAccount = postingAccountResponse.data.account;\n                          console.log(`Auto-assigned posting account: ${postingAccount}`);\n                          console.log(postingAccountResponse.data.success, postingAccountResponse.data.account)\n                        } else {\n                          // If API call succeeded but no account returned, log and keep 'Auto Assign'\n                          console.error('Auto Assign API returned no account');\n                          console.log(`Auto-assigned posting account: ${postingAccount}`);\n                          console.log(postingAccountResponse.data.success, postingAccountResponse.data.account)\n                        }\n                      } catch (autoAssignError) {\n                        console.error('Error auto-assigning posting account:', autoAssignError);\n                        // Keep 'Auto Assign' if the API call fails - don't default to anything else\n                      }\n                    }\n\n                    if (postingAccountField) {\n                      // Set Posting Account custom field value on the card\n                      await axios.put(\n                        `https://api.trello.com/1/customFields/${postingAccountField.id}?key=${apiKey}&token=${apiToken}`,\n                        { value: { text: postingAccount } },\n                        {\n                          headers: {\n                            'Accept': 'application/json',\n                            'Content-Type': 'application/json'\n                          }\n                        }\n                      );\n                    }\n\n                    // Insert into ab_video table with project_id and variable_id\n                    await client.query(\n                      `INSERT INTO ab_video (trello_card_id, variable_id, project_id)\n                       VALUES ($1, $2, $3)`,\n                      [trelloCardId, variableId, projectId]\n                    );\n\n                    // Get the account_id for the final posting account (whether original, auto-assigned, or Auto Assign)\n                    if (postingAccount && postingAccount !== 'Auto Assign') {\n                      try {\n                        const finalAccountResult = await client.query(\n                          `SELECT id FROM posting_accounts WHERE account = $1`,\n                          [postingAccount]\n                        );\n\n                        if (finalAccountResult.rows.length > 0) {\n                          accountId = finalAccountResult.rows[0].id;\n                        }\n                      } catch (error) {\n                        console.error('Error getting final account ID:', error);\n                        // Continue without account_id if there's an error\n                      }\n                    }\n\n                    // Insert script into AB script table with writer_id=129\n                    const insertRes = await client.query(\n                      `INSERT INTO ab_script (\n                         title,\n                         google_doc_link,\n                         approval_status,\n                         created_at,\n                         writer_id,\n                         account_id\n                       )\n                       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5)\n                       RETURNING *`,\n                      [\n                        cardTitle,\n                        resource.googleDocLink,\n                        \"Approved Script. Ready for production\",\n                        129, // Always set writer_id to 129\n                        accountId // Use the account_id we determined, or null if not found/Auto Assign\n                      ]\n                    );\n\n                    const script = insertRes.rows[0];\n\n                    // Update script with Trello card ID\n                    await client.query(\n                      \"UPDATE ab_script SET trello_card_id = $1 WHERE id = $2\",\n                      [trelloCardId, script.id]\n                    );\n                  } catch (trelloError) {\n                    console.error('Error creating AB Trello card:', trelloError);\n                    // Continue even if Trello card creation fails\n                  }\n                } catch (trelloError) {\n                  console.error('Error creating AB Trello card:', trelloError);\n                  // Continue even if Trello card creation fails\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // Commit the transaction\n      await client.query('COMMIT');\n\n      return NextResponse.json({\n        success: true,\n        projectId,\n        message: 'AB Project created successfully'\n      });\n    } catch (dbError) {\n      // Rollback in case of error\n      await client.query('ROLLBACK');\n      console.error('Database error:', dbError);\n      throw dbError;\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error creating AB project:', error);\n    return NextResponse.json(\n      { error: 'Failed to create AB project' },\n      { status: 500 }\n    );\n  }\n}\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;;;;;AAEA,wCAAwC;AACxC,MAAM,gBAAgB;IACpB,MAAM,SAAS;QACb,MAAM;QACN,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM;QACN,KAAK;YAAE,oBAAoB;QAAM;IACnC;IAEA,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;AAEtB,oBAAoB;AACpB,KAAK,EAAE,CAAC,SAAS,CAAC;IAChB,QAAQ,KAAK,CAAC,mCAAmC;IACjD,QAAQ,IAAI,CAAC,CAAC;AAChB;AAEA,qCAAqC;AACrC,MAAM,uBAAuB,4BAA4B,uCAAuC;AAEzF,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QAEtC,iBAAiB;QACjB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,KAAK,OAAO;QAEjC,IAAI;YACF,sBAAsB;YACtB,MAAM,OAAO,KAAK,CAAC;YAEnB,+BAA+B;YAC/B,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC;;qBAEY,CAAC,EACd;gBACE,YAAY,IAAI;gBAChB,YAAY,WAAW,IAAI;gBAC3B,EAAE,wBAAwB;aAC3B;YAGH,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;YAE1C,iCAAiC;YACjC,IAAI,YAAY,SAAS,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG;gBAC7D,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;oBAC5C,IAAI,CAAC,SAAS,IAAI,EAAE;oBAEpB,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC,CAAC;;yBAEY,CAAC,EACd;wBAAC;wBAAW,SAAS,IAAI;wBAAE,SAAS,IAAI,IAAI;qBAAG;oBAGjD,MAAM,aAAa,eAAe,IAAI,CAAC,EAAE,CAAC,EAAE;oBAE5C,mBAAmB;oBACnB,IAAI,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;wBACvD,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;4BACzC,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,UAAU,EAAE;4BAErD,wCAAwC;4BACxC,MAAM,OAAO,KAAK,CAChB,CAAC;;6BAEY,CAAC,EACd;gCACE;gCACA,SAAS,aAAa,IAAI;gCAC1B,SAAS,UAAU,IAAI;gCACvB,SAAS,IAAI,IAAI;gCACjB,SAAS,OAAO,IAAI;6BACrB;4BAGH,4EAA4E;4BAC5E,IAAI,YAAY;4BAEhB,IAAI;gCACF,kFAAkF;gCAClF,IAAI,SAAS,OAAO,IAAI,SAAS,OAAO,KAAK,eAAe;oCAC1D,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC,kDAAkD,CAAC,EACpD;wCAAC,SAAS,OAAO;qCAAC;oCAGpB,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,GAAG;wCACjC,YAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;oCACtC;gCACF;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,kDAAkD;4BACpD;4BAEA,0EAA0E;4BAC1E,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,IAAI;gCAC5D,gEAAgE;gCAChE,MAAM,mBAAmB,MAAM,OAAO,KAAK,CACzC,CAAC,6DAA6D,CAAC,EAC/D;oCAAC,SAAS,UAAU;iCAAC;gCAGvB,IAAI,iBAAiB,IAAI,CAAC,MAAM,GAAG,GAAG;oCACpC,yDAAyD;oCACzD,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,EAAE,CAAC,SAAS;oCAC3D,MAAM,oBAAoB,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU;oCAE7D,IAAI,oBAAoB,mBAAmB;wCACzC,iFAAiF;wCACjF,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY,SAAS,UAAU;yCAAC;oCAEhD,OAAO,IAAI,oBAAoB,CAAC,mBAAmB;wCACjD,oEAAoE;wCACpE,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY;4CAAW,SAAS,UAAU;yCAAC;oCAE3D,OAAO,IAAI,CAAC,oBAAoB,mBAAmB;wCACjD,qEAAqE;wCACrE,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY;4CAAK,SAAS,UAAU;yCAAC;oCAErD,OAAO;wCACL,iCAAiC;wCACjC,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY;4CAAK;4CAAW,SAAS,UAAU;yCAAC;oCAEhE;gCACF,OAAO;oCACL,mFAAmF;oCACnF,MAAM,OAAO,KAAK,CAChB,CAAC;mEAC8C,CAAC,EAChD;wCACE,SAAS,UAAU;wCACnB;wCACA;wCACA;wCACA,IAAI,8BAA8B;qCACnC;gCAEL;4BACF;4BAEA,6EAA6E;4BAC7E,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,IAAI,OAAO,MAC5D,CAAC,CAAC,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,EAAE,GAAG;gCAC/D,IAAI;oCACF,8CAA8C;oCAC9C,MAAM,gBAAgB,SAAS,SAAS,CAAC,OAAO,CAAC,YAAY;oCAE7D,kFAAkF;oCAClF,MAAM,YAAY,CAAC,GAAG,EAAE,YAAY,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,eAAe;oCAE7E,IAAI;wCACF,+CAA+C;wCAC/C,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC;wCAGF,IAAI,eAAe,IAAI,CAAC,MAAM,KAAK,GAAG;4CACpC,QAAQ,KAAK,CAAC;4CACd;wCACF;wCAEA,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,eAAe,IAAI,CAAC,EAAE;wCAEnE,uDAAuD;wCACvD,MAAM,eAAe,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CACnC,kCACA,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,MAAM;gDACN,MAAM,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE;gDAClD,KAAK;4CACP;wCACF;wCAGF,MAAM,eAAe,aAAa,IAAI,CAAC,EAAE;wCAEzC,iBAAiB;wCACjB,MAAM,uIAAA,CAAA,UAAK,CAAC,IAAI,CACd,CAAC,+BAA+B,EAAE,aAAa,YAAY,CAAC,EAC5D,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,KAAK,SAAS,aAAa;4CAC7B;wCACF;wCAGF,4DAA4D;wCAC5D,MAAM,cAAc,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CACjC,CAAC,+BAA+B,EAAE,cAAc,EAChD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,UAAU,YAAY,IAAI,CAAC,OAAO;wCAExC,MAAM,eAAe,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAClC,CAAC,gCAAgC,EAAE,QAAQ,aAAa,CAAC,EACzD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,sBAAsB,aAAa,IAAI,CAAC,IAAI,CAChD,CAAA,IAAK,EAAE,IAAI,KAAK;wCAGlB,IAAI,iBAAiB,SAAS,OAAO;wCAErC,4CAA4C;wCAC5C,IAAI,mBAAmB,eAAe;4CACpC,IAAI;gDACF,wCAAwC;gDACxC,MAAM,yBAAyB,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAC5C,+DACA;oDACE,QAAQ;wDACN,gBAAgB;wDAChB,oBAAoB;oDACtB;gDACF;gDAGF,+DAA+D;gDAC/D,IAAI,uBAAuB,IAAI,IAAI,uBAAuB,IAAI,CAAC,OAAO,EAAE;oDACtE,iBAAiB,uBAAuB,IAAI,CAAC,OAAO;oDACpD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB;oDAC9D,QAAQ,GAAG,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE,uBAAuB,IAAI,CAAC,OAAO;gDACtF,OAAO;oDACL,4EAA4E;oDAC5E,QAAQ,KAAK,CAAC;oDACd,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB;oDAC9D,QAAQ,GAAG,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE,uBAAuB,IAAI,CAAC,OAAO;gDACtF;4CACF,EAAE,OAAO,iBAAiB;gDACxB,QAAQ,KAAK,CAAC,yCAAyC;4CACvD,4EAA4E;4CAC9E;wCACF;wCAEA,IAAI,qBAAqB;4CACvB,qDAAqD;4CACrD,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CACb,CAAC,sCAAsC,EAAE,oBAAoB,EAAE,CAAC,KAAK,EAAE,OAAO,OAAO,EAAE,UAAU,EACjG;gDAAE,OAAO;oDAAE,MAAM;gDAAe;4CAAE,GAClC;gDACE,SAAS;oDACP,UAAU;oDACV,gBAAgB;gDAClB;4CACF;wCAEJ;wCAEA,6DAA6D;wCAC7D,MAAM,OAAO,KAAK,CAChB,CAAC;0CACmB,CAAC,EACrB;4CAAC;4CAAc;4CAAY;yCAAU;wCAGvC,qGAAqG;wCACrG,IAAI,kBAAkB,mBAAmB,eAAe;4CACtD,IAAI;gDACF,MAAM,qBAAqB,MAAM,OAAO,KAAK,CAC3C,CAAC,kDAAkD,CAAC,EACpD;oDAAC;iDAAe;gDAGlB,IAAI,mBAAmB,IAAI,CAAC,MAAM,GAAG,GAAG;oDACtC,YAAY,mBAAmB,IAAI,CAAC,EAAE,CAAC,EAAE;gDAC3C;4CACF,EAAE,OAAO,OAAO;gDACd,QAAQ,KAAK,CAAC,mCAAmC;4CACjD,kDAAkD;4CACpD;wCACF;wCAEA,wDAAwD;wCACxD,MAAM,YAAY,MAAM,OAAO,KAAK,CAClC,CAAC;;;;;;;;;kCASW,CAAC,EACb;4CACE;4CACA,SAAS,aAAa;4CACtB;4CACA;4CACA,UAAU,qEAAqE;yCAChF;wCAGH,MAAM,SAAS,UAAU,IAAI,CAAC,EAAE;wCAEhC,oCAAoC;wCACpC,MAAM,OAAO,KAAK,CAChB,0DACA;4CAAC;4CAAc,OAAO,EAAE;yCAAC;oCAE7B,EAAE,OAAO,aAAa;wCACpB,QAAQ,KAAK,CAAC,kCAAkC;oCAChD,8CAA8C;oCAChD;gCACF,EAAE,OAAO,aAAa;oCACpB,QAAQ,KAAK,CAAC,kCAAkC;gCAChD,8CAA8C;gCAChD;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,yBAAyB;YACzB,MAAM,OAAO,KAAK,CAAC;YAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,SAAS;YAChB,4BAA4B;YAC5B,MAAM,OAAO,KAAK,CAAC;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA8B,GACvC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}