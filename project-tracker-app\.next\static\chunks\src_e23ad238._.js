(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/RechartsLineChart.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RechartsLineChart)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/chart/LineChart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/Line.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/XAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/YAxis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/cartesian/CartesianGrid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/component/Tooltip.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$ResponsiveContainer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/recharts/es6/component/ResponsiveContainer.js [app-client] (ecmascript)");
'use client';
;
;
// Custom tooltip component
const CustomTooltip = ({ active, payload, label })=>{
    if (active && payload && payload.length) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-gray-800 text-white p-2 rounded-md shadow-lg text-xs",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "font-medium",
                    children: label
                }, void 0, false, {
                    fileName: "[project]/src/components/RechartsLineChart.tsx",
                    lineNumber: 32,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: "font-bold",
                    children: payload[0].value?.toLocaleString()
                }, void 0, false, {
                    fileName: "[project]/src/components/RechartsLineChart.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/RechartsLineChart.tsx",
            lineNumber: 31,
            columnNumber: 7
        }, this);
    }
    return null;
};
_c = CustomTooltip;
function RechartsLineChart({ data, label, color = '#4f46e5', height = 150 }) {
    // If no data, show a message
    if (!data || data.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-gray-400 text-xs p-2 h-[150px] flex items-center justify-center border border-gray-200 rounded",
            children: "No data available yet"
        }, void 0, false, {
            fileName: "[project]/src/components/RechartsLineChart.tsx",
            lineNumber: 49,
            columnNumber: 7
        }, this);
    }
    // Format data for Recharts
    const chartData = data.map((item)=>({
            date: item.date,
            value: item.value
        }));
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-sm font-medium text-gray-700 mb-1",
                children: label
            }, void 0, false, {
                fileName: "[project]/src/components/RechartsLineChart.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                style: {
                    width: '100%',
                    height: `${height}px`
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$ResponsiveContainer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ResponsiveContainer"], {
                    width: "100%",
                    height: "100%",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$chart$2f$LineChart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LineChart"], {
                        data: chartData,
                        margin: {
                            top: 5,
                            right: 5,
                            left: 0,
                            bottom: 5
                        },
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("defs", {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("linearGradient", {
                                    id: `color-${label}`,
                                    x1: "0",
                                    y1: "0",
                                    x2: "0",
                                    y2: "1",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "5%",
                                            stopColor: color,
                                            stopOpacity: 0.2
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/RechartsLineChart.tsx",
                                            lineNumber: 72,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("stop", {
                                            offset: "95%",
                                            stopColor: color,
                                            stopOpacity: 0
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/RechartsLineChart.tsx",
                                            lineNumber: 73,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/RechartsLineChart.tsx",
                                    lineNumber: 71,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 70,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$CartesianGrid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CartesianGrid"], {
                                strokeDasharray: "3 3",
                                vertical: false,
                                stroke: "#f0f0f0"
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 76,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$XAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["XAxis"], {
                                dataKey: "date",
                                tick: {
                                    fontSize: 10
                                },
                                tickLine: false,
                                axisLine: {
                                    stroke: '#e5e7eb'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 77,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$YAxis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["YAxis"], {
                                tick: {
                                    fontSize: 10
                                },
                                tickFormatter: (value)=>value.toLocaleString(),
                                tickLine: false,
                                axisLine: false,
                                width: 35
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 83,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$component$2f$Tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tooltip"], {
                                content: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CustomTooltip, {}, void 0, false, {
                                    fileName: "[project]/src/components/RechartsLineChart.tsx",
                                    lineNumber: 91,
                                    columnNumber: 24
                                }, void 0),
                                cursor: {
                                    stroke: color,
                                    strokeWidth: 1,
                                    strokeDasharray: '3 3'
                                }
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 90,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$recharts$2f$es6$2f$cartesian$2f$Line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Line"], {
                                type: "monotone",
                                dataKey: "value",
                                stroke: color,
                                strokeWidth: 2,
                                dot: data.length < 10 ? {
                                    stroke: color,
                                    strokeWidth: 2,
                                    r: 3
                                } : false,
                                activeDot: {
                                    stroke: 'white',
                                    strokeWidth: 2,
                                    r: 5,
                                    fill: color
                                },
                                name: label,
                                fill: `url(#color-${label})`
                            }, void 0, false, {
                                fileName: "[project]/src/components/RechartsLineChart.tsx",
                                lineNumber: 94,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/RechartsLineChart.tsx",
                        lineNumber: 66,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/RechartsLineChart.tsx",
                    lineNumber: 65,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/RechartsLineChart.tsx",
                lineNumber: 64,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/RechartsLineChart.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, this);
}
_c1 = RechartsLineChart;
var _c, _c1;
__turbopack_context__.k.register(_c, "CustomTooltip");
__turbopack_context__.k.register(_c1, "RechartsLineChart");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/dashboard/projects/[id]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ProjectDetail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/fa/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$RechartsLineChart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/RechartsLineChart.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/date-fns/format.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
// Helper function to format time series data for charts
const formatTimeSeriesData = (data, field = 'views')=>{
    if (!data || !data.length) {
        // Return empty array if no data
        return [];
    }
    try {
        // Check if all data points are from the same day
        const dates = data.map((item)=>new Date(item.date || new Date()));
        const allSameDay = dates.length > 0 && dates.every((date)=>date.getDate() === dates[0].getDate() && date.getMonth() === dates[0].getMonth() && date.getFullYear() === dates[0].getFullYear());
        // If all data is from the same day and we have few points, show hours instead of days
        const formatString = allSameDay && dates.length < 5 ? 'h:mm a' : 'MMM dd';
        return data.map((item)=>({
                date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(new Date(item.date || new Date()), formatString),
                value: Math.round(item[field] || 0)
            }));
    } catch (error) {
        console.error(`Error formatting time series data for ${field}:`, error);
        return [];
    }
};
// Helper function to calculate median
const calculateMedian = (values)=>{
    if (!values || !values.length) return 0;
    const sorted = [
        ...values
    ].sort((a, b)=>a - b);
    const middle = Math.floor(sorted.length / 2);
    if (sorted.length % 2 === 0) {
        return Math.round((sorted[middle - 1] + sorted[middle]) / 2);
    }
    return Math.round(sorted[middle]);
};
// Function to fetch real video metrics from InfluxDB
const fetchVideoMetrics = async (videoUrl)=>{
    if (!videoUrl || videoUrl.trim() === '') {
        return [];
    }
    try {
        const encodedUrl = encodeURIComponent(videoUrl);
        const response = await fetch(`/api/metrics?type=video&videoUrl=${encodedUrl}`);
        const data = await response.json();
        return data.metrics || [];
    } catch (error) {
        console.error(`Error fetching metrics for video ${videoUrl}:`, error);
        return [];
    }
};
// Function to fetch video thumbnail from InfluxDB
const fetchVideoThumbnail = async (videoUrl)=>{
    if (!videoUrl || videoUrl.trim() === '') {
        return '';
    }
    try {
        const encodedUrl = encodeURIComponent(videoUrl);
        const response = await fetch(`/api/metrics?type=thumbnail&videoUrl=${encodedUrl}`);
        const data = await response.json();
        return data.thumbnailUrl || '';
    } catch (error) {
        console.error(`Error fetching thumbnail for video ${videoUrl}:`, error);
        return '';
    }
};
// Function to fetch project metrics from our new API endpoint
const fetchProjectMetrics = async (projectId, variableId = null)=>{
    try {
        let url = `/api/metrics/project?projectId=${projectId}`;
        if (variableId) {
            url += `&variableId=${variableId}`;
        }
        const response = await fetch(url);
        const data = await response.json();
        // Check if this is a single video (added in the updated getAggregateMetrics function)
        const isSingleVideo = data.aggregateMetrics?.isSingleVideo || false;
        return {
            avgViews: Math.round(data.aggregateMetrics?.views?.avg || 0),
            avgLikes: Math.round(data.aggregateMetrics?.likes?.avg || 0),
            avgComments: Math.round(data.aggregateMetrics?.comments?.avg || 0),
            medianViews: Math.round(data.aggregateMetrics?.views?.median || 0),
            medianLikes: Math.round(data.aggregateMetrics?.likes?.median || 0),
            medianComments: Math.round(data.aggregateMetrics?.comments?.median || 0),
            viewsOverTime: formatTimeSeriesData(data.timeSeries || [], 'views'),
            likesOverTime: formatTimeSeriesData(data.timeSeries || [], 'likes'),
            commentsOverTime: formatTimeSeriesData(data.timeSeries || [], 'comments'),
            metrics: data.metrics || [],
            isSingleVideo: isSingleVideo
        };
    } catch (error) {
        console.error(`Error fetching metrics for project/variable:`, error);
        return {
            avgViews: 0,
            avgLikes: 0,
            avgComments: 0,
            medianViews: 0,
            medianLikes: 0,
            medianComments: 0,
            viewsOverTime: [],
            likesOverTime: [],
            commentsOverTime: [],
            metrics: [],
            isSingleVideo: false
        };
    }
};
// Function to fetch real variable metrics from InfluxDB
const fetchVariableMetrics = async (videoUrls)=>{
    if (!videoUrls || !videoUrls.length) {
        return {
            avgViews: 0,
            avgLikes: 0,
            avgComments: 0,
            medianViews: 0,
            medianLikes: 0,
            medianComments: 0,
            viewsOverTime: [],
            likesOverTime: [],
            commentsOverTime: [],
            isSingleVideo: false
        };
    }
    try {
        // Check if this is a single video
        const isSingleVideo = videoUrls.length === 1;
        // Encode all URLs
        const encodedUrls = videoUrls.map((url)=>encodeURIComponent(url));
        // Fetch aggregate metrics
        const aggregateResponse = await fetch(`/api/metrics?type=aggregate&videoUrls=${encodedUrls.join(',')}`);
        const aggregateData = await aggregateResponse.json();
        // Fetch time series data
        const timeSeriesResponse = await fetch(`/api/metrics?type=timeseries&videoUrls=${encodedUrls.join(',')}`);
        const timeSeriesData = await timeSeriesResponse.json();
        // Get variable metrics for median calculation
        const variableResponse = await fetch(`/api/metrics?type=variable&videoUrls=${encodedUrls.join(',')}`);
        const variableData = await variableResponse.json();
        // Use the median values from the aggregate metrics
        return {
            avgViews: Math.round(aggregateData.metrics?.views?.avg || 0),
            avgLikes: Math.round(aggregateData.metrics?.likes?.avg || 0),
            avgComments: Math.round(aggregateData.metrics?.comments?.avg || 0),
            medianViews: Math.round(aggregateData.metrics?.views?.median || 0),
            medianLikes: Math.round(aggregateData.metrics?.likes?.median || 0),
            medianComments: Math.round(aggregateData.metrics?.comments?.median || 0),
            viewsOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'views'),
            likesOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'likes'),
            commentsOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'comments'),
            isSingleVideo: isSingleVideo || aggregateData.metrics?.isSingleVideo || false
        };
    } catch (error) {
        console.error(`Error fetching metrics for variable:`, error);
        return {
            avgViews: 0,
            avgLikes: 0,
            avgComments: 0,
            medianViews: 0,
            medianLikes: 0,
            medianComments: 0,
            viewsOverTime: [],
            likesOverTime: [],
            commentsOverTime: [],
            isSingleVideo: videoUrls.length === 1
        };
    }
};
// Function to fetch YouTube videos for a variable
const fetchVariableVideos = async (variableId)=>{
    try {
        const response = await fetch(`/api/videos?variableId=${variableId}`);
        if (!response.ok) throw new Error('Failed to fetch videos');
        const data = await response.json();
        // Map the response to the format expected by the rest of the code
        return data.videos.map((video)=>({
                id: video.id,
                url: video.url,
                title: video.title || `YouTube Video ${video.id}`,
                created: video.created,
                writer_id: video.writer_id,
                account_id: video.account_id,
                video_cat: video.video_cat
            }));
    } catch (error) {
        console.error(`Error fetching videos for variable ${variableId}:`, error);
        return [];
    }
};
// Fallback mock data generator for when real data is unavailable
const generateMockTimeData = (days = 30, baseValue = 1000, volatility = 0.2)=>{
    const data = [];
    let currentValue = baseValue;
    for(let i = 0; i < days; i++){
        // Generate a date days ago from today
        const date = new Date();
        date.setDate(date.getDate() - (days - i));
        // Random walk with volatility
        const change = currentValue * (Math.random() * volatility * 2 - volatility);
        currentValue = Math.max(0, currentValue + change);
        data.push({
            date: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$date$2d$fns$2f$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["format"])(date, 'MMM dd'),
            value: Math.round(currentValue)
        });
    }
    return data;
};
// We're now using the RechartsLineChart component instead of a custom LineChart
const LineChart = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$RechartsLineChart$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
function ProjectDetail() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const params = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const [project, setProject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [variables, setVariables] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [selectedVariable, setSelectedVariable] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProjectDetail.useEffect": ()=>{
            // Load project from the database
            const loadProject = {
                "ProjectDetail.useEffect.loadProject": async ()=>{
                    try {
                        // Get the ID from params
                        const id = params?.id;
                        if (!id) return;
                        // Try to fetch from the API
                        const response = await fetch(`/api/projects/${id}`);
                        if (response.ok) {
                            const data = await response.json();
                            const project = data.project;
                            if (project) {
                                // Format the project data
                                const formattedProject = {
                                    id: project.id,
                                    name: project.name,
                                    description: project.description,
                                    dueDate: new Date(project.created_at).toISOString().split('T')[0],
                                    createdBy: project.created_by,
                                    members: project.members || 1
                                };
                                setProject(formattedProject);
                                // Process variables and their resources
                                if (project.variables && project.variables.length > 0) {
                                    // Process each variable
                                    const processVariablesWithMetrics = {
                                        "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": async ()=>{
                                            const processedVariables = [];
                                            for (const variable of project.variables){
                                                // Get YouTube videos for this variable from the video table
                                                // We'll fetch videos directly from the video table using the variable ID
                                                const youtubeResources = await fetchVariableVideos(variable.id);
                                                // If we have YouTube resources, fetch metrics from InfluxDB
                                                let variableMetrics = null;
                                                let videos = [];
                                                if (youtubeResources.length > 0) {
                                                    // Get video URLs for InfluxDB queries
                                                    const videoUrls = youtubeResources.map({
                                                        "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.videoUrls": (r)=>r.url
                                                    }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.videoUrls"]);
                                                    // Fetch metrics for this variable using the project_id and variable_id
                                                    variableMetrics = await fetchProjectMetrics(project.id, variable.id);
                                                    // Process individual videos with their metrics
                                                    for (const resource of youtubeResources){
                                                        try {
                                                            // Fetch metrics for this specific video
                                                            const videoMetrics = await fetchVideoMetrics(resource.url);
                                                            // Fetch thumbnail for this video
                                                            const thumbnailUrl = await fetchVideoThumbnail(resource.url);
                                                            // Calculate views, likes, and comments from metrics
                                                            let views = 0;
                                                            let likes = 0;
                                                            let comments = 0;
                                                            let viewsData = [];
                                                            let likesData = [];
                                                            let commentsData = [];
                                                            if (videoMetrics && videoMetrics.length > 0) {
                                                                // Get the latest metrics
                                                                const latestMetrics = videoMetrics[videoMetrics.length - 1];
                                                                views = latestMetrics.views || 0;
                                                                likes = latestMetrics.likes || 0;
                                                                comments = latestMetrics.comments || 0;
                                                                // Format time series data using our helper function
                                                                viewsData = formatTimeSeriesData(videoMetrics.map({
                                                                    "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (m)=>({
                                                                            date: m._time,
                                                                            views: m.views || 0
                                                                        })
                                                                }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"]), 'views');
                                                                likesData = formatTimeSeriesData(videoMetrics.map({
                                                                    "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (m)=>({
                                                                            date: m._time,
                                                                            likes: m.likes || 0
                                                                        })
                                                                }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"]), 'likes');
                                                                commentsData = formatTimeSeriesData(videoMetrics.map({
                                                                    "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (m)=>({
                                                                            date: m._time,
                                                                            comments: m.comments || 0
                                                                        })
                                                                }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"]), 'comments');
                                                            } else {
                                                                // Use zeros for metrics when no data is available
                                                                views = 0;
                                                                likes = 0;
                                                                comments = 0;
                                                                viewsData = [];
                                                                likesData = [];
                                                                commentsData = [];
                                                            }
                                                            videos.push({
                                                                id: `video-${resource.id}`,
                                                                title: resource.title,
                                                                url: resource.url,
                                                                thumbnail: thumbnailUrl,
                                                                views,
                                                                likes,
                                                                comments,
                                                                viewsData,
                                                                likesData,
                                                                commentsData
                                                            });
                                                        } catch (videoError) {
                                                            console.error(`Error processing video ${resource.url}:`, videoError);
                                                        }
                                                    }
                                                }
                                                // No need to generate mock videos - if there are no videos with metrics, we'll show empty state
                                                // If we don't have metrics from InfluxDB, calculate them from videos
                                                if (!variableMetrics) {
                                                    const allViews = videos.map({
                                                        "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allViews": (v)=>v.views
                                                    }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allViews"]);
                                                    const allLikes = videos.map({
                                                        "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allLikes": (v)=>v.likes
                                                    }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allLikes"]);
                                                    const allComments = videos.map({
                                                        "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allComments": (v)=>v.comments
                                                    }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics.allComments"]);
                                                    // Check if this is a single video
                                                    const isSingleVideo = videos.length === 1;
                                                    // For a single video, we'll use the individual video metrics directly
                                                    // For multiple videos, we'll calculate averages and medians
                                                    let avgViews, avgLikes, avgComments, medianViews, medianLikes, medianComments;
                                                    if (isSingleVideo && videos.length > 0) {
                                                        // Use the individual video metrics directly
                                                        avgViews = videos[0].views;
                                                        avgLikes = videos[0].likes;
                                                        avgComments = videos[0].comments || 0;
                                                        medianViews = videos[0].views;
                                                        medianLikes = videos[0].likes;
                                                        medianComments = videos[0].comments || 0;
                                                    } else {
                                                        // Calculate averages and medians for multiple videos
                                                        avgViews = allViews.length > 0 ? Math.round(allViews.reduce({
                                                            "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (a, b)=>a + b
                                                        }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"], 0) / allViews.length) : 0;
                                                        avgLikes = allLikes.length > 0 ? Math.round(allLikes.reduce({
                                                            "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (a, b)=>a + b
                                                        }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"], 0) / allLikes.length) : 0;
                                                        avgComments = allComments.length > 0 ? Math.round(allComments.reduce({
                                                            "ProjectDetail.useEffect.loadProject.processVariablesWithMetrics": (a, b)=>a + b
                                                        }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"], 0) / allComments.length) : 0;
                                                        medianViews = calculateMedian(allViews);
                                                        medianLikes = calculateMedian(allLikes);
                                                        medianComments = calculateMedian(allComments);
                                                    }
                                                    variableMetrics = {
                                                        avgViews,
                                                        avgLikes,
                                                        avgComments,
                                                        medianViews,
                                                        medianLikes,
                                                        medianComments,
                                                        viewsOverTime: [],
                                                        likesOverTime: [],
                                                        commentsOverTime: [],
                                                        isSingleVideo
                                                    };
                                                }
                                                processedVariables.push({
                                                    id: `var-${variable.id}`,
                                                    name: variable.name,
                                                    note: variable.note || 'No description provided',
                                                    videos,
                                                    metrics: variableMetrics
                                                });
                                            }
                                            setVariables(processedVariables);
                                            setLoading(false);
                                        }
                                    }["ProjectDetail.useEffect.loadProject.processVariablesWithMetrics"];
                                    processVariablesWithMetrics();
                                } else {
                                    // Fallback to a single variable if no variables found
                                    setVariables([
                                        {
                                            id: 'var-mock',
                                            name: 'Sample Variable',
                                            note: 'This is a sample variable for testing',
                                            videos: [],
                                            metrics: {
                                                avgViews: 0,
                                                avgLikes: 0,
                                                medianViews: 0,
                                                medianLikes: 0,
                                                viewsOverTime: [],
                                                likesOverTime: [],
                                                isSingleVideo: true
                                            }
                                        }
                                    ]);
                                    setLoading(false);
                                }
                            } else {
                                setLoading(false);
                            }
                        } else {
                            // Fallback to localStorage if API fails
                            const savedProjects = localStorage.getItem('projects');
                            const id = params?.id;
                            if (savedProjects && id) {
                                const projects = JSON.parse(savedProjects);
                                const project = projects.find({
                                    "ProjectDetail.useEffect.loadProject.project": (p)=>p.id.toString() === id.toString()
                                }["ProjectDetail.useEffect.loadProject.project"]);
                                if (project) {
                                    setProject(project);
                                    // Generate mock variable data based on actual project variables
                                    const mockVariables = [];
                                    // If we have actual variables in the project
                                    if (project.variables && project.variables.length > 0) {
                                        // Create mock data for each actual variable
                                        for(let i = 0; i < project.variables.length; i++){
                                            const variable = project.variables[i];
                                            const mockVideoCount = Math.floor(Math.random() * 3) + 2;
                                            const videos = [];
                                            for(let j = 0; j < mockVideoCount; j++){
                                                const views = Math.floor(Math.random() * 50000) + 5000;
                                                const likes = Math.floor(views * (Math.random() * 0.1 + 0.05));
                                                videos.push({
                                                    id: `mock-video-${j}`,
                                                    title: `Sample YouTube Video ${j + 1}`,
                                                    url: `https://youtube.com/watch?v=sample${j}`,
                                                    views,
                                                    likes,
                                                    viewsData: generateMockTimeData(30, views, 0.1),
                                                    likesData: generateMockTimeData(30, likes, 0.15)
                                                });
                                            }
                                            // Calculate metrics
                                            const allViews = videos.map({
                                                "ProjectDetail.useEffect.loadProject.allViews": (v)=>v.views
                                            }["ProjectDetail.useEffect.loadProject.allViews"]);
                                            const allLikes = videos.map({
                                                "ProjectDetail.useEffect.loadProject.allLikes": (v)=>v.likes
                                            }["ProjectDetail.useEffect.loadProject.allLikes"]);
                                            // Check if this is a single video
                                            const isSingleVideo = videos.length === 1;
                                            // For a single video, use the individual video metrics directly
                                            let avgViews, avgLikes, medianViews, medianLikes;
                                            if (isSingleVideo && videos.length > 0) {
                                                // Use the individual video metrics directly
                                                avgViews = videos[0].views;
                                                avgLikes = videos[0].likes;
                                                medianViews = videos[0].views;
                                                medianLikes = videos[0].likes;
                                            } else {
                                                // Calculate averages and medians for multiple videos
                                                avgViews = Math.round(allViews.reduce({
                                                    "ProjectDetail.useEffect.loadProject": (a, b)=>a + b
                                                }["ProjectDetail.useEffect.loadProject"], 0) / allViews.length);
                                                avgLikes = Math.round(allLikes.reduce({
                                                    "ProjectDetail.useEffect.loadProject": (a, b)=>a + b
                                                }["ProjectDetail.useEffect.loadProject"], 0) / allLikes.length);
                                                medianViews = calculateMedian(allViews);
                                                medianLikes = calculateMedian(allLikes);
                                            }
                                            mockVariables.push({
                                                id: `var-${i}`,
                                                name: variable.name || `Variable ${i + 1}`,
                                                note: variable.note || 'No description provided',
                                                videos,
                                                metrics: {
                                                    avgViews,
                                                    avgLikes,
                                                    medianViews,
                                                    medianLikes,
                                                    viewsOverTime: generateMockTimeData(30, avgViews, 0.05),
                                                    likesOverTime: generateMockTimeData(30, avgLikes, 0.08),
                                                    isSingleVideo
                                                }
                                            });
                                        }
                                    } else {
                                        // Fallback to a single variable if no variables found
                                        mockVariables.push({
                                            id: 'var-mock',
                                            name: 'Sample Variable',
                                            note: 'This is a sample variable for testing',
                                            videos: [],
                                            metrics: {
                                                avgViews: 0,
                                                avgLikes: 0,
                                                medianViews: 0,
                                                medianLikes: 0,
                                                viewsOverTime: [],
                                                likesOverTime: [],
                                                isSingleVideo: true
                                            }
                                        });
                                    }
                                    setVariables(mockVariables);
                                }
                                setLoading(false);
                            } else {
                                setLoading(false);
                            }
                        }
                    } catch (error) {
                        console.error('Error loading project:', error);
                        setLoading(false);
                    }
                }
            }["ProjectDetail.useEffect.loadProject"];
            loadProject();
        }
    }["ProjectDetail.useEffect"], [
        params?.id
    ]);
    // Helper function to extract YouTube video ID from URL
    const extractYouTubeVideoId = (url)=>{
        if (!url) return null;
        const pattern = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?\/)|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
        const match = url.match(pattern);
        return match ? match[1] : null;
    };
    const handleBack = ()=>{
        router.push('/dashboard');
    };
    const handleVariableClick = (variable)=>{
        setSelectedVariable(variable);
    };
    const closeVariableDetail = ()=>{
        setSelectedVariable(null);
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex items-center justify-center h-full",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                lineNumber: 605,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
            lineNumber: 604,
            columnNumber: 7
        }, this);
    }
    if (!project) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "p-6",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 text-red-500 p-4 rounded-lg",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-lg font-medium",
                        children: "Project Not Found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 614,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-2",
                        children: "The project you're looking for doesn't exist or has been deleted."
                    }, void 0, false, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 615,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleBack,
                        className: "mt-4 flex items-center text-indigo-600 hover:text-indigo-800",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaArrowLeft"], {
                                className: "mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 620,
                                columnNumber: 13
                            }, this),
                            " Back to Dashboard"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 616,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                lineNumber: 613,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
            lineNumber: 612,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-6 space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleBack,
                                className: "flex items-center text-indigo-600 hover:text-indigo-800 mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaArrowLeft"], {
                                        className: "mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 636,
                                        columnNumber: 13
                                    }, this),
                                    " Back to Dashboard"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 632,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                className: "text-2xl font-bold text-gray-800",
                                children: project.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 638,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-gray-500",
                                children: [
                                    "Created on ",
                                    project.dueDate
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 639,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 631,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-right",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-gray-500",
                                children: "Total Variables"
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 642,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-2xl font-bold",
                                children: variables.length
                            }, void 0, false, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 643,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 641,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                lineNumber: 630,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",
                children: variables.map((variable)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer",
                        onClick: ()=>handleVariableClick(variable),
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-gradient-to-r from-indigo-500 to-purple-600 p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-lg font-semibold text-white",
                                        children: variable.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 656,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-indigo-100 text-sm truncate",
                                        children: variable.note
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 657,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 655,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-4 mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: variable.metrics.isSingleVideo ? 'Total Views' : 'Avg. Views'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 663,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.isSingleVideo && variable.videos.length > 0 ? variable.videos[0].views.toLocaleString() : variable.metrics.avgViews.toLocaleString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 666,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 662,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: variable.metrics.isSingleVideo ? 'Total Likes' : 'Avg. Likes'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 673,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.isSingleVideo && variable.videos.length > 0 ? variable.videos[0].likes.toLocaleString() : variable.metrics.avgLikes.toLocaleString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 676,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 672,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: variable.metrics.isSingleVideo ? 'Total Comments' : 'Avg. Comments'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 683,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.isSingleVideo && variable.videos.length > 0 ? variable.videos[0].comments?.toLocaleString() || '0' : variable.metrics.avgComments?.toLocaleString() || '0'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 686,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 682,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 661,
                                        columnNumber: 15
                                    }, this),
                                    !variable.metrics.isSingleVideo && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-4 mb-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: "Median Views"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 697,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.medianViews.toLocaleString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 698,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 696,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: "Median Likes"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 703,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.medianLikes.toLocaleString()
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 704,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 702,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-sm font-medium text-gray-700",
                                                        children: "Median Comments"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 709,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xl font-bold text-gray-900",
                                                        children: variable.metrics.medianComments?.toLocaleString() || '0'
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 710,
                                                        columnNumber: 21
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 708,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 695,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                data: variable.metrics.viewsOverTime,
                                                label: "Views Over Time",
                                                color: "#4f46e5",
                                                height: 80
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 718,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                data: variable.metrics.likesOverTime,
                                                label: "Likes Over Time",
                                                color: "#7c3aed",
                                                height: 80
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 724,
                                                columnNumber: 17
                                            }, this),
                                            variable.metrics.commentsOverTime && variable.metrics.commentsOverTime.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                data: variable.metrics.commentsOverTime,
                                                label: "Comments Over Time",
                                                color: "#10b981",
                                                height: 80
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 731,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 717,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 flex items-center justify-between text-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-gray-500",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "font-medium",
                                                        children: variable.videos.length
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 742,
                                                        columnNumber: 19
                                                    }, this),
                                                    " videos"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 741,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-indigo-600 flex items-center",
                                                children: [
                                                    "View Details ",
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaChartLine"], {
                                                        className: "ml-1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 745,
                                                        columnNumber: 32
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 744,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 740,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                lineNumber: 660,
                                columnNumber: 13
                            }, this)
                        ]
                    }, variable.id, true, {
                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                        lineNumber: 650,
                        columnNumber: 11
                    }, this))
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                lineNumber: 648,
                columnNumber: 7
            }, this),
            selectedVariable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-xl shadow-xl w-full max-w-4xl my-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-gradient-to-r from-indigo-500 to-purple-600 p-4 rounded-t-xl flex justify-between items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-xl font-semibold text-white",
                                            children: selectedVariable.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                            lineNumber: 759,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-indigo-100 text-sm",
                                            children: selectedVariable.note
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                            lineNumber: 760,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 758,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: closeVariableDetail,
                                    className: "text-white hover:text-indigo-200",
                                    children: "×"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 762,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                            lineNumber: 757,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6 max-h-[80vh] overflow-y-auto",
                            children: [
                                selectedVariable.metrics.isSingleVideo ? // For a single video, show just one box with total metrics
                                // Use the individual video metrics directly instead of the aggregate metrics
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-6",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "bg-indigo-50 p-4 rounded-lg shadow-sm",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "text-sm font-medium text-gray-700 mb-2",
                                                children: "Total Performance"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 776,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "grid grid-cols-3 gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center text-indigo-700 font-medium",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaEye"], {
                                                                        className: "mr-1"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 780,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    " Views"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 779,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-2xl font-bold text-gray-900",
                                                                children: selectedVariable.videos[0]?.views.toLocaleString() || '0'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 782,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 778,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center text-indigo-700 font-medium",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaThumbsUp"], {
                                                                        className: "mr-1"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 788,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    " Likes"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 787,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-2xl font-bold text-gray-900",
                                                                children: selectedVariable.videos[0]?.likes.toLocaleString() || '0'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 790,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 786,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "flex items-center text-indigo-700 font-medium",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                        className: "mr-1",
                                                                        children: "💬"
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 796,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    " Comments"
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 795,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "text-2xl font-bold text-gray-900",
                                                                children: selectedVariable.videos[0]?.comments.toLocaleString() || '0'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 798,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 794,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 777,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                        lineNumber: 775,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 774,
                                    columnNumber: 17
                                }, this) : // For multiple videos, show both average and median
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-2 gap-6 mb-6",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-indigo-50 p-4 rounded-lg shadow-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm font-medium text-gray-700 mb-2",
                                                    children: "Average Performance"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                    lineNumber: 809,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-3 gap-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-indigo-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaEye"], {
                                                                            className: "mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 813,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Views"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 812,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.avgViews.toLocaleString()
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 815,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 811,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-indigo-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaThumbsUp"], {
                                                                            className: "mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 821,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Likes"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 820,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.avgLikes.toLocaleString()
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 823,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 819,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-indigo-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "mr-1",
                                                                            children: "💬"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 829,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Comments"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 828,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.avgComments?.toLocaleString() || '0'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 831,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 827,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                    lineNumber: 810,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                            lineNumber: 808,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-purple-50 p-4 rounded-lg shadow-sm",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "text-sm font-medium text-gray-700 mb-2",
                                                    children: "Median Performance"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                    lineNumber: 839,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "grid grid-cols-3 gap-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-purple-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaEye"], {
                                                                            className: "mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 843,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Views"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 842,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.medianViews.toLocaleString()
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 845,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 841,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-purple-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaThumbsUp"], {
                                                                            className: "mr-1"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 851,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Likes"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 850,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.medianLikes.toLocaleString()
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 853,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 849,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "flex items-center text-purple-700 font-medium",
                                                                    children: [
                                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                            className: "mr-1",
                                                                            children: "💬"
                                                                        }, void 0, false, {
                                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                            lineNumber: 859,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        " Comments"
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 858,
                                                                    columnNumber: 25
                                                                }, this),
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "text-2xl font-bold text-gray-900",
                                                                    children: selectedVariable.metrics.medianComments?.toLocaleString() || '0'
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 861,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                            lineNumber: 857,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                    lineNumber: 840,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                            lineNumber: 838,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 807,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium text-gray-800 mb-4",
                                    children: "Individual Videos"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 870,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-4",
                                    children: selectedVariable.videos.map((video)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-col md:flex-row gap-4",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:w-1/3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "bg-gray-200 rounded-lg aspect-video flex items-center justify-center overflow-hidden",
                                                                children: video.thumbnail ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("img", {
                                                                    src: video.thumbnail,
                                                                    alt: video.title,
                                                                    className: "w-full h-full object-cover"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 879,
                                                                    columnNumber: 29
                                                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaYoutube"], {
                                                                    className: "text-red-600 text-4xl"
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                    lineNumber: 885,
                                                                    columnNumber: 29
                                                                }, this)
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 877,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "mt-2",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                                        className: "font-medium text-gray-800",
                                                                        children: video.title
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 889,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                                        href: video.url,
                                                                        target: "_blank",
                                                                        rel: "noopener noreferrer",
                                                                        className: "text-sm text-blue-600 hover:underline flex items-center mt-1",
                                                                        onClick: (e)=>e.stopPropagation(),
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$fa$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FaLink"], {
                                                                                className: "mr-1"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 897,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            " View on YouTube"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 890,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 888,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 876,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "md:w-2/3",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "grid grid-cols-3 gap-4 mb-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm font-medium text-gray-700",
                                                                                children: "Total Views"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 905,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-xl font-bold text-gray-900",
                                                                                children: video.views.toLocaleString()
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 906,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 904,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm font-medium text-gray-700",
                                                                                children: "Total Likes"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 911,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-xl font-bold text-gray-900",
                                                                                children: video.likes.toLocaleString()
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 912,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 910,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                        children: [
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-sm font-medium text-gray-700",
                                                                                children: "Total Comments"
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 917,
                                                                                columnNumber: 29
                                                                            }, this),
                                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                                className: "text-xl font-bold text-gray-900",
                                                                                children: video.comments.toLocaleString()
                                                                            }, void 0, false, {
                                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                                lineNumber: 918,
                                                                                columnNumber: 29
                                                                            }, this)
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 916,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 903,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                className: "space-y-4",
                                                                children: [
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                                        data: video.viewsData,
                                                                        label: "Views Over Time",
                                                                        color: "#4f46e5",
                                                                        height: 100
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 925,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                                        data: video.likesData,
                                                                        label: "Likes Over Time",
                                                                        color: "#7c3aed",
                                                                        height: 100
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 931,
                                                                        columnNumber: 27
                                                                    }, this),
                                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LineChart, {
                                                                        data: video.commentsData,
                                                                        label: "Comments Over Time",
                                                                        color: "#10b981",
                                                                        height: 100
                                                                    }, void 0, false, {
                                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                        lineNumber: 937,
                                                                        columnNumber: 27
                                                                    }, this)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                                lineNumber: 924,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                        lineNumber: 902,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                                lineNumber: 875,
                                                columnNumber: 21
                                            }, this)
                                        }, video.id, false, {
                                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                            lineNumber: 874,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                                    lineNumber: 872,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                            lineNumber: 770,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                    lineNumber: 756,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
                lineNumber: 755,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/dashboard/projects/[id]/page.tsx",
        lineNumber: 628,
        columnNumber: 5
    }, this);
}
_s(ProjectDetail, "t7yWsBiCDKrs4k7DTy1lq6uFU18=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"]
    ];
});
_c = ProjectDetail;
var _c;
__turbopack_context__.k.register(_c, "ProjectDetail");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_e23ad238._.js.map