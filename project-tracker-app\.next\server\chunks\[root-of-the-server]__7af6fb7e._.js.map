{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/lib/db.ts"], "sourcesContent": ["import { Pool, PoolConfig } from 'pg';\n\n// Parse the service account credentials from environment variable\nlet credentials: any = null;\ntry {\n  if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {\n    credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);\n  }\n} catch (error) {\n  console.error('Error parsing Google credentials:', error);\n}\n\n// Cloud SQL connection configuration\nconst getPoolConfig = (): PoolConfig => {\n  // Log environment variables for debugging\n  console.log('Environment variables:');\n  console.log('USE_CLOUD_SQL_AUTH_PROXY:', process.env.USE_CLOUD_SQL_AUTH_PROXY);\n  console.log('DB_HOST:', process.env.DB_HOST);\n  console.log('DB_PORT:', process.env.DB_PORT);\n  console.log('NODE_ENV:', process.env.NODE_ENV);\n\n  // Force direct connection regardless of environment variable\n  // This is a temporary fix to ensure we're using direct connection\n  const config = {\n    host: '***********', // Hardcoded for now\n    database: process.env.DB_NAME || 'postgres',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASS || 'Plotpointe!@3456',\n    port: 5432, // Hardcoded for now\n    ssl: { rejectUnauthorized: false } // Enable SSL with rejectUnauthorized: false\n  };\n\n  console.log('Using database config:', JSON.stringify(config, null, 2));\n  return config;\n};\n\n// Create the connection pool\nconst pool = new Pool(getPoolConfig());\n\n// Add error handler to log connection issues\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nexport default pool;\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,kEAAkE;AAClE,IAAI,cAAmB;AACvB,IAAI;IACF,IAAI,QAAQ,GAAG,CAAC,mCAAmC,EAAE;QACnD,cAAc,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,mCAAmC;IAC1E;AACF,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,qCAAqC;AACrD;AAEA,qCAAqC;AACrC,MAAM,gBAAgB;IACpB,0CAA0C;IAC1C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,GAAG,CAAC,wBAAwB;IAC7E,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC;IAEZ,6DAA6D;IAC7D,kEAAkE;IAClE,MAAM,SAAS;QACb,MAAM;QACN,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM;QACN,KAAK;YAAE,oBAAoB;QAAM,EAAE,4CAA4C;IACjF;IAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,QAAQ,MAAM;IACnE,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;AAEtB,6CAA6C;AAC7C,KAAK,EAAE,CAAC,SAAS,CAAC;IAChB,QAAQ,KAAK,CAAC,mCAAmC;IACjD,QAAQ,IAAI,CAAC,CAAC;AAChB;uCAEe", "debugId": null}}, {"offset": {"line": 230, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport pool from '@/lib/db';\n\nexport async function GET(request: NextRequest) {\n  try {\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Query the database for projects\n      const result = await client.query(\n        `SELECT p.id, p.name, p.description, p.created_at, p.created_by, p.members,\n                COUNT(pv.id) as variable_count\n         FROM src_projects p\n         LEFT JOIN src_project_variables pv ON p.id = pv.project_id\n         GROUP BY p.id\n         ORDER BY p.created_at DESC`\n      );\n\n      return NextResponse.json({ projects: result.rows });\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error fetching projects:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch projects' },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const projectData = await request.json();\n\n    // Validate input\n    if (!projectData.name) {\n      return NextResponse.json(\n        { error: 'Project name is required' },\n        { status: 400 }\n      );\n    }\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Start a transaction\n      await client.query('BEGIN');\n\n      // Insert project\n      const projectResult = await client.query(\n        `INSERT INTO src_projects (name, description, members)\n         VALUES ($1, $2, $3)\n         RETURNING id`,\n        [\n          projectData.name,\n          projectData.description || '',\n          1 // Default members count\n        ]\n      );\n\n      const projectId = projectResult.rows[0].id;\n\n      // Insert variables and resources\n      if (projectData.variables && projectData.variables.length > 0) {\n        for (const variable of projectData.variables) {\n          if (!variable.name) continue;\n\n          const variableResult = await client.query(\n            `INSERT INTO src_project_variables (project_id, name, note)\n             VALUES ($1, $2, $3)\n             RETURNING id`,\n            [projectId, variable.name, variable.note || '']\n          );\n\n          const variableId = variableResult.rows[0].id;\n\n          // Insert resources\n          if (variable.resources && variable.resources.length > 0) {\n            for (const resource of variable.resources) {\n              if (!resource.googleDocLink && !resource.youtubeUrl) continue;\n\n              // Insert into src_variable_resources\n              await client.query(\n                `INSERT INTO src_variable_resources (variable_id, google_doc_link, youtube_url, note, account)\n                 VALUES ($1, $2, $3, $4, $5)\n                 RETURNING id`,\n                [\n                  variableId,\n                  resource.googleDocLink || '',\n                  resource.youtubeUrl || '',\n                  resource.note || '',\n                  resource.account || ''\n                ]\n              );\n\n              // Get the account_id from the posting_accounts table\n              let accountId = null;\n\n              // Try to get the account ID from the posting_accounts table\n              try {\n                const accountResult = await client.query(\n                  `SELECT id FROM posting_accounts WHERE account = $1`,\n                  [resource.account || 'clay'] // Default to 'clay' if not specified\n                );\n\n                if (accountResult.rows.length > 0) {\n                  accountId = accountResult.rows[0].id;\n                }\n              } catch (error) {\n                console.error('Error getting account ID:', error);\n                // Continue without account_id if there's an error\n              }\n\n              // If it's a YouTube URL, check if it already exists in the video table\n              if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {\n                // Check if the YouTube URL already exists in the video table\n                const videoCheckResult = await client.query(\n                  `SELECT id, writer_id, account_id FROM video WHERE url = $1`,\n                  [resource.youtubeUrl]\n                );\n\n                if (videoCheckResult.rows.length > 0) {\n                  // URL exists, check if it has a writer_id and account_id\n                  const existingWriterId = videoCheckResult.rows[0].writer_id;\n                  const existingAccountId = videoCheckResult.rows[0].account_id;\n\n                  if (existingWriterId && existingAccountId) {\n                    // If both writer_id and account_id exist, just update project_id and variable_id\n                    await client.query(\n                      `UPDATE video\n                       SET project_id = $1, variable_id = $2\n                       WHERE url = $3`,\n                      [projectId, variableId, resource.youtubeUrl]\n                    );\n                  } else if (existingWriterId && !existingAccountId) {\n                    // If writer_id exists but no account_id, update with new account_id\n                    await client.query(\n                      `UPDATE video\n                       SET project_id = $1, variable_id = $2, account_id = $3\n                       WHERE url = $4`,\n                      [projectId, variableId, accountId || 1, resource.youtubeUrl]\n                    );\n                  } else if (!existingWriterId && existingAccountId) {\n                    // If account_id exists but no writer_id, update with writer_id = 129\n                    await client.query(\n                      `UPDATE video\n                       SET project_id = $1, variable_id = $2, writer_id = $3\n                       WHERE url = $4`,\n                      [projectId, variableId, 129, resource.youtubeUrl]\n                    );\n                  } else {\n                    // If neither exists, update both\n                    await client.query(\n                      `UPDATE video\n                       SET project_id = $1, variable_id = $2, writer_id = $3, account_id = $4\n                       WHERE url = $5`,\n                      [projectId, variableId, 129, accountId || 1, resource.youtubeUrl]\n                    );\n                  }\n                } else {\n                  // URL doesn't exist, insert it into the video table with the actual variable_id\n                  await client.query(\n                    `INSERT INTO video (url, account_id, created, project_id, variable_id, writer_id)\n                     VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`,\n                    [\n                      resource.youtubeUrl,\n                      accountId || 1, // Default to account ID 1 if not found\n                      projectId,\n                      variableId, // Use the actual variable ID\n                      129 // Always set writer_id to 129\n                    ]\n                  );\n                }\n              }\n\n              // Only create a Trello card if there's a Google Doc link AND no YouTube link\n              if (resource.googleDocLink && resource.googleDocLink.trim() !== '' &&\n                  (!resource.youtubeUrl || resource.youtubeUrl.trim() === '')) {\n                try {\n                  // Get the resource index within this variable\n                  const resourceIndex = variable.resources.indexOf(resource) + 1;\n\n                  // Create a title in the format: {project name}: {variable name}:{resource number}\n                  const cardTitle = `${projectData.name}: ${variable.name}:${resourceIndex}`;\n\n                  // Import axios for making HTTP requests from the server\n                  const axios = require('axios');\n\n                  try {\n                    // Call the Trello API endpoint directly\n                    const trelloData = {\n                      title: cardTitle,\n                      googleDocLink: resource.googleDocLink,\n                      posting_account: resource.account || 'clay',\n                      variable_id: variableId,\n                      project_id: projectId\n                    };\n\n                    // Fetch Trello settings\n                    const settingsResult = await client.query(\n                      \"SELECT api_key, token FROM settings ORDER BY id DESC LIMIT 1\"\n                    );\n\n                    if (settingsResult.rows.length === 0) {\n                      console.error('Trello settings not configured');\n                      return;\n                    }\n\n                    const { api_key: apiKey, token: apiToken } = settingsResult.rows[0];\n\n                    // Create the Trello card directly\n                    const autoApprovedListID = \"66982de89e8cb1bfb456ba0a\";\n\n                    // Create the card\n                    const cardResponse = await axios.post(\n                      'https://api.trello.com/1/cards',\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          idList: autoApprovedListID,\n                          name: cardTitle,\n                          desc: `Google Doc Link: ${resource.googleDocLink}`,\n                          pos: 'top'\n                        }\n                      }\n                    );\n\n                    const trelloCardId = cardResponse.data.id;\n\n                    // Add attachment\n                    await axios.post(\n                      `https://api.trello.com/1/cards/${trelloCardId}/attachments`,\n                      null,\n                      {\n                        params: {\n                          key: apiKey,\n                          token: apiToken,\n                          url: resource.googleDocLink\n                        }\n                      }\n                    );\n\n                    // Discover board and custom-field IDs (for Posting Account)\n                    const cardDetails = await axios.get(\n                      `https://api.trello.com/1/cards/${trelloCardId}`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const boardId = cardDetails.data.idBoard;\n\n                    const customFields = await axios.get(\n                      `https://api.trello.com/1/boards/${boardId}/customFields`,\n                      { params: { key: apiKey, token: apiToken } }\n                    );\n\n                    const postingAccountField = customFields.data.find(\n                      f => f.name === \"Posting Account\"\n                    );\n\n                    if (postingAccountField) {\n                      // Set Posting Account custom field\n                      await axios.put(\n                        `https://api.trello.com/1/cards/${trelloCardId}/customField/${postingAccountField.id}/item`,\n                        { value: { text: resource.account || 'clay' } },\n                        { params: { key: apiKey, token: apiToken } }\n                      );\n                    }\n\n                    // Insert into video table with project_id and variable_id\n                    await client.query(\n                      `INSERT INTO video (trello_card_id, variable_id, project_id)\n                       VALUES ($1, $2, $3)`,\n                      [trelloCardId, variableId, projectId]\n                    );\n\n                    // Insert script into DB with writer_id=129\n                    const insertRes = await client.query(\n                      `INSERT INTO script (\n                         title,\n                         google_doc_link,\n                         approval_status,\n                         created_at,\n                         writer_id,\n                         account_id\n                       )\n                       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5)\n                       RETURNING *`,\n                      [\n                        cardTitle,\n                        resource.googleDocLink,\n                        \"Approved Script. Ready for production\",\n                        129, // Always set writer_id to 129\n                        accountId || 1 // Use the account_id we got earlier or default to 1\n                      ]\n                    );\n\n                    const script = insertRes.rows[0];\n\n                    // Update script with Trello card ID\n                    await client.query(\n                      \"UPDATE script SET trello_card_id = $1 WHERE id = $2\",\n                      [trelloCardId, script.id]\n                    );\n                  } catch (trelloError) {\n                    console.error('Error creating Trello card:', trelloError);\n                    // Continue even if Trello card creation fails\n                  }\n\n\n                } catch (trelloError) {\n                  console.error('Error creating Trello card:', trelloError);\n                  // Continue even if Trello card creation fails\n                }\n              }\n            }\n          }\n        }\n      }\n\n      // Commit the transaction\n      await client.query('COMMIT');\n\n      return NextResponse.json({\n        success: true,\n        projectId,\n        message: 'Project created successfully'\n      });\n    } catch (dbError) {\n      // Rollback in case of error\n      await client.query('ROLLBACK');\n      console.error('Database error:', dbError);\n      throw dbError;\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error creating project:', error);\n    return NextResponse.json(\n      { error: 'Failed to create project' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,kCAAkC;YAClC,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,CAAC;;;;;mCAK0B,CAAC;YAG9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,UAAU,OAAO,IAAI;YAAC;QACnD,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QAEtC,iBAAiB;QACjB,IAAI,CAAC,YAAY,IAAI,EAAE;YACrB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA2B,GACpC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,sBAAsB;YACtB,MAAM,OAAO,KAAK,CAAC;YAEnB,iBAAiB;YACjB,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC;;qBAEY,CAAC,EACd;gBACE,YAAY,IAAI;gBAChB,YAAY,WAAW,IAAI;gBAC3B,EAAE,wBAAwB;aAC3B;YAGH,MAAM,YAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;YAE1C,iCAAiC;YACjC,IAAI,YAAY,SAAS,IAAI,YAAY,SAAS,CAAC,MAAM,GAAG,GAAG;gBAC7D,KAAK,MAAM,YAAY,YAAY,SAAS,CAAE;oBAC5C,IAAI,CAAC,SAAS,IAAI,EAAE;oBAEpB,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC,CAAC;;yBAEY,CAAC,EACd;wBAAC;wBAAW,SAAS,IAAI;wBAAE,SAAS,IAAI,IAAI;qBAAG;oBAGjD,MAAM,aAAa,eAAe,IAAI,CAAC,EAAE,CAAC,EAAE;oBAE5C,mBAAmB;oBACnB,IAAI,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,MAAM,GAAG,GAAG;wBACvD,KAAK,MAAM,YAAY,SAAS,SAAS,CAAE;4BACzC,IAAI,CAAC,SAAS,aAAa,IAAI,CAAC,SAAS,UAAU,EAAE;4BAErD,qCAAqC;4BACrC,MAAM,OAAO,KAAK,CAChB,CAAC;;6BAEY,CAAC,EACd;gCACE;gCACA,SAAS,aAAa,IAAI;gCAC1B,SAAS,UAAU,IAAI;gCACvB,SAAS,IAAI,IAAI;gCACjB,SAAS,OAAO,IAAI;6BACrB;4BAGH,qDAAqD;4BACrD,IAAI,YAAY;4BAEhB,4DAA4D;4BAC5D,IAAI;gCACF,MAAM,gBAAgB,MAAM,OAAO,KAAK,CACtC,CAAC,kDAAkD,CAAC,EACpD;oCAAC,SAAS,OAAO,IAAI;iCAAO,CAAC,qCAAqC;;gCAGpE,IAAI,cAAc,IAAI,CAAC,MAAM,GAAG,GAAG;oCACjC,YAAY,cAAc,IAAI,CAAC,EAAE,CAAC,EAAE;gCACtC;4BACF,EAAE,OAAO,OAAO;gCACd,QAAQ,KAAK,CAAC,6BAA6B;4BAC3C,kDAAkD;4BACpD;4BAEA,uEAAuE;4BACvE,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,IAAI;gCAC5D,6DAA6D;gCAC7D,MAAM,mBAAmB,MAAM,OAAO,KAAK,CACzC,CAAC,0DAA0D,CAAC,EAC5D;oCAAC,SAAS,UAAU;iCAAC;gCAGvB,IAAI,iBAAiB,IAAI,CAAC,MAAM,GAAG,GAAG;oCACpC,yDAAyD;oCACzD,MAAM,mBAAmB,iBAAiB,IAAI,CAAC,EAAE,CAAC,SAAS;oCAC3D,MAAM,oBAAoB,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU;oCAE7D,IAAI,oBAAoB,mBAAmB;wCACzC,iFAAiF;wCACjF,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY,SAAS,UAAU;yCAAC;oCAEhD,OAAO,IAAI,oBAAoB,CAAC,mBAAmB;wCACjD,oEAAoE;wCACpE,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY,aAAa;4CAAG,SAAS,UAAU;yCAAC;oCAEhE,OAAO,IAAI,CAAC,oBAAoB,mBAAmB;wCACjD,qEAAqE;wCACrE,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY;4CAAK,SAAS,UAAU;yCAAC;oCAErD,OAAO;wCACL,iCAAiC;wCACjC,MAAM,OAAO,KAAK,CAChB,CAAC;;qCAEc,CAAC,EAChB;4CAAC;4CAAW;4CAAY;4CAAK,aAAa;4CAAG,SAAS,UAAU;yCAAC;oCAErE;gCACF,OAAO;oCACL,gFAAgF;oCAChF,MAAM,OAAO,KAAK,CAChB,CAAC;mEAC8C,CAAC,EAChD;wCACE,SAAS,UAAU;wCACnB,aAAa;wCACb;wCACA;wCACA,IAAI,8BAA8B;qCACnC;gCAEL;4BACF;4BAEA,6EAA6E;4BAC7E,IAAI,SAAS,aAAa,IAAI,SAAS,aAAa,CAAC,IAAI,OAAO,MAC5D,CAAC,CAAC,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,IAAI,OAAO,EAAE,GAAG;gCAC/D,IAAI;oCACF,8CAA8C;oCAC9C,MAAM,gBAAgB,SAAS,SAAS,CAAC,OAAO,CAAC,YAAY;oCAE7D,kFAAkF;oCAClF,MAAM,YAAY,GAAG,YAAY,IAAI,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,CAAC,EAAE,eAAe;oCAE1E,wDAAwD;oCACxD,MAAM;oCAEN,IAAI;wCACF,wCAAwC;wCACxC,MAAM,aAAa;4CACjB,OAAO;4CACP,eAAe,SAAS,aAAa;4CACrC,iBAAiB,SAAS,OAAO,IAAI;4CACrC,aAAa;4CACb,YAAY;wCACd;wCAEA,wBAAwB;wCACxB,MAAM,iBAAiB,MAAM,OAAO,KAAK,CACvC;wCAGF,IAAI,eAAe,IAAI,CAAC,MAAM,KAAK,GAAG;4CACpC,QAAQ,KAAK,CAAC;4CACd;wCACF;wCAEA,MAAM,EAAE,SAAS,MAAM,EAAE,OAAO,QAAQ,EAAE,GAAG,eAAe,IAAI,CAAC,EAAE;wCAEnE,kCAAkC;wCAClC,MAAM,qBAAqB;wCAE3B,kBAAkB;wCAClB,MAAM,eAAe,MAAM,MAAM,IAAI,CACnC,kCACA,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,QAAQ;gDACR,MAAM;gDACN,MAAM,CAAC,iBAAiB,EAAE,SAAS,aAAa,EAAE;gDAClD,KAAK;4CACP;wCACF;wCAGF,MAAM,eAAe,aAAa,IAAI,CAAC,EAAE;wCAEzC,iBAAiB;wCACjB,MAAM,MAAM,IAAI,CACd,CAAC,+BAA+B,EAAE,aAAa,YAAY,CAAC,EAC5D,MACA;4CACE,QAAQ;gDACN,KAAK;gDACL,OAAO;gDACP,KAAK,SAAS,aAAa;4CAC7B;wCACF;wCAGF,4DAA4D;wCAC5D,MAAM,cAAc,MAAM,MAAM,GAAG,CACjC,CAAC,+BAA+B,EAAE,cAAc,EAChD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,UAAU,YAAY,IAAI,CAAC,OAAO;wCAExC,MAAM,eAAe,MAAM,MAAM,GAAG,CAClC,CAAC,gCAAgC,EAAE,QAAQ,aAAa,CAAC,EACzD;4CAAE,QAAQ;gDAAE,KAAK;gDAAQ,OAAO;4CAAS;wCAAE;wCAG7C,MAAM,sBAAsB,aAAa,IAAI,CAAC,IAAI,CAChD,CAAA,IAAK,EAAE,IAAI,KAAK;wCAGlB,IAAI,qBAAqB;4CACvB,mCAAmC;4CACnC,MAAM,MAAM,GAAG,CACb,CAAC,+BAA+B,EAAE,aAAa,aAAa,EAAE,oBAAoB,EAAE,CAAC,KAAK,CAAC,EAC3F;gDAAE,OAAO;oDAAE,MAAM,SAAS,OAAO,IAAI;gDAAO;4CAAE,GAC9C;gDAAE,QAAQ;oDAAE,KAAK;oDAAQ,OAAO;gDAAS;4CAAE;wCAE/C;wCAEA,0DAA0D;wCAC1D,MAAM,OAAO,KAAK,CAChB,CAAC;0CACmB,CAAC,EACrB;4CAAC;4CAAc;4CAAY;yCAAU;wCAGvC,2CAA2C;wCAC3C,MAAM,YAAY,MAAM,OAAO,KAAK,CAClC,CAAC;;;;;;;;;kCASW,CAAC,EACb;4CACE;4CACA,SAAS,aAAa;4CACtB;4CACA;4CACA,aAAa,EAAE,oDAAoD;yCACpE;wCAGH,MAAM,SAAS,UAAU,IAAI,CAAC,EAAE;wCAEhC,oCAAoC;wCACpC,MAAM,OAAO,KAAK,CAChB,uDACA;4CAAC;4CAAc,OAAO,EAAE;yCAAC;oCAE7B,EAAE,OAAO,aAAa;wCACpB,QAAQ,KAAK,CAAC,+BAA+B;oCAC7C,8CAA8C;oCAChD;gCAGF,EAAE,OAAO,aAAa;oCACpB,QAAQ,KAAK,CAAC,+BAA+B;gCAC7C,8CAA8C;gCAChD;4BACF;wBACF;oBACF;gBACF;YACF;YAEA,yBAAyB;YACzB,MAAM,OAAO,KAAK,CAAC;YAEnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT;gBACA,SAAS;YACX;QACF,EAAE,OAAO,SAAS;YAChB,4BAA4B;YAC5B,MAAM,OAAO,KAAK,CAAC;YACnB,QAAQ,KAAK,CAAC,mBAAmB;YACjC,MAAM;QACR,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}