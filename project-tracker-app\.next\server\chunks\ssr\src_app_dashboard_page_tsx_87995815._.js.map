{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport {\n  FaPlus,\n  FaFolder,\n  FaFolderOpen,\n  FaCalendarAlt,\n  FaUsers,\n  FaTasks,\n  FaTrash,\n  FaLink,\n  FaYoutube,\n  FaFileAlt,\n  FaUserAlt,\n  FaEdit,\n  FaPencilAlt\n} from 'react-icons/fa';\n\nexport default function Dashboard() {\n  const router = useRouter();\n  const [showNewProjectModal, setShowNewProjectModal] = useState(false);\n  const [projects, setProjects] = useState<Array<{\n    id: number;\n    name: string;\n    tasks: number;\n    members: number;\n    dueDate: string;\n  }>>([]);\n\n  const [accounts, setAccounts] = useState<{id: number, account: string}[]>([]);\n  const [newProject, setNewProject] = useState({\n    name: '',\n    description: '',\n    variables: [\n      {\n        name: '',\n        note: '',\n        resources: [\n          {\n            googleDocLink: '',\n            note: '',\n            youtubeUrl: '',\n            account: 'Auto Assign'  // Set default to Auto Assign\n          }\n        ]\n      }\n    ]\n  });\n\n  // Load projects from the database\n  useEffect(() => {\n    const fetchProjects = async () => {\n      try {\n        // Try to fetch from the API\n        const response = await fetch('/api/projects');\n\n        if (response.ok) {\n          const data = await response.json();\n\n          // Transform the data to match our expected format\n          const formattedProjects = data.projects.map((p: any) => ({\n            id: p.id,\n            name: p.name,\n            tasks: parseInt(p.variable_count) || 0,\n            members: p.members || 1,\n            dueDate: new Date(p.created_at).toISOString().split('T')[0]\n          }));\n\n          setProjects(formattedProjects);\n        } else {\n          // If API fails, try to load from localStorage as fallback\n          const savedProjects = localStorage.getItem('projects');\n          if (savedProjects) {\n            try {\n              setProjects(JSON.parse(savedProjects));\n            } catch (error) {\n              console.error('Error parsing saved projects:', error);\n            }\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching projects:', error);\n\n        // Fallback to localStorage\n        const savedProjects = localStorage.getItem('projects');\n        if (savedProjects) {\n          try {\n            setProjects(JSON.parse(savedProjects));\n          } catch (error) {\n            console.error('Error parsing saved projects:', error);\n          }\n        }\n      }\n    };\n\n    fetchProjects();\n  }, []);\n\n  // Fetch accounts from the API\n  useEffect(() => {\n    const fetchAccounts = async () => {\n      try {\n        // First try the regular accounts endpoint\n        try {\n          console.log('Trying to fetch accounts from /api/accounts...');\n          const response = await fetch('/api/accounts');\n\n          if (!response.ok) {\n            throw new Error('Failed to fetch accounts from regular endpoint');\n          }\n\n          const data = await response.json();\n          console.log('Successfully fetched accounts from /api/accounts:', data);\n          // Add Auto Assign to the accounts array\n          setAccounts([{ id: 0, account: 'Auto Assign' }, ...data.accounts]);\n          return; // Exit if successful\n        } catch (apiError) {\n          console.error('Regular API error:', apiError);\n          // If regular endpoint fails, try the direct endpoint\n        }\n\n        // Try the direct accounts endpoint\n        try {\n          console.log('Trying to fetch accounts from /api/direct-accounts...');\n          const directResponse = await fetch('/api/direct-accounts');\n\n          if (!directResponse.ok) {\n            throw new Error('Failed to fetch accounts from direct endpoint');\n          }\n\n          const directData = await directResponse.json();\n          console.log('Successfully fetched accounts from /api/direct-accounts:', directData);\n          // Add Auto Assign to the accounts array\n          setAccounts([{ id: 0, account: 'Auto Assign' }, ...directData.accounts]);\n          return; // Exit if successful\n        } catch (directApiError) {\n          console.error('Direct API error:', directApiError);\n          // If direct endpoint fails, try the debug endpoint\n        }\n\n        // Try the debug endpoint to get more information\n        try {\n          console.log('Trying to fetch debug information...');\n          const debugResponse = await fetch('/api/debug');\n\n          if (debugResponse.ok) {\n            const debugData = await debugResponse.json();\n            console.log('Debug information:', debugData);\n          }\n        } catch (debugError) {\n          console.error('Debug API error:', debugError);\n        }\n\n        // Fallback to mock data as last resort\n        console.log('Falling back to mock data');\n        setAccounts([\n          { id: 0, account: 'Auto Assign' },\n          { id: 1, account: 'account1' },\n          { id: 2, account: 'account2' },\n          { id: 3, account: 'account3' }\n        ]);\n      } catch (error) {\n        console.error('Error in account fetching process:', error);\n      }\n    };\n\n    fetchAccounts();\n  }, []);\n\n  // Add a new variable to the project\n  const addVariable = () => {\n    setNewProject({\n      ...newProject,\n      variables: [\n        ...newProject.variables,\n        {\n          name: '',\n          note: '',\n          resources: [\n            {\n              googleDocLink: '',\n              note: '',\n              youtubeUrl: ''\n            }\n          ]\n        }\n      ]\n    });\n  };\n\n  // Add a new resource to a variable\n  const addResource = (variableIndex: number) => {\n    const updatedVariables = [...newProject.variables];\n    updatedVariables[variableIndex].resources.push({\n      googleDocLink: '',\n      note: '',\n      youtubeUrl: '',\n      account: 'Auto Assign'  // Set default to Auto Assign\n    });\n\n    setNewProject({\n      ...newProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Remove a variable\n  const removeVariable = (index: number) => {\n    const updatedVariables = newProject.variables.filter((_, i) => i !== index);\n    setNewProject({\n      ...newProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Remove a resource\n  const removeResource = (variableIndex: number, resourceIndex: number) => {\n    const updatedVariables = [...newProject.variables];\n    updatedVariables[variableIndex].resources = updatedVariables[variableIndex].resources.filter((_, i) => i !== resourceIndex);\n\n    setNewProject({\n      ...newProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Update variable field\n  const updateVariableField = (variableIndex: number, field: string, value: string) => {\n    const updatedVariables = [...newProject.variables];\n    updatedVariables[variableIndex] = {\n      ...updatedVariables[variableIndex],\n      [field]: value\n    };\n\n    setNewProject({\n      ...newProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Update resource field\n  const updateResourceField = (variableIndex: number, resourceIndex: number, field: string, value: string) => {\n    const updatedVariables = [...newProject.variables];\n    updatedVariables[variableIndex].resources[resourceIndex] = {\n      ...updatedVariables[variableIndex].resources[resourceIndex],\n      [field]: value\n    };\n\n    setNewProject({\n      ...newProject,\n      variables: updatedVariables\n    });\n  };\n\n  const [createProjectLoading, setCreateProjectLoading] = useState(false);\n  const [createProjectError, setCreateProjectError] = useState('');\n\n  // Edit project state\n  const [showEditProjectModal, setShowEditProjectModal] = useState(false);\n  const [editProject, setEditProject] = useState<any>(null);\n  const [editProjectLoading, setEditProjectLoading] = useState(false);\n  const [editProjectError, setEditProjectError] = useState('');\n\n  // Function to clear all projects (for testing)\n  const clearAllProjects = () => {\n    setProjects([]);\n    localStorage.removeItem('projects');\n  };\n\n  // Function to handle opening the edit modal\n  const handleEditProject = async (e: React.MouseEvent, projectId: number) => {\n    e.stopPropagation(); // Prevent navigation to project detail page\n\n    try {\n      // Fetch the full project data from the API\n      const response = await fetch(`/api/projects/${projectId}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch project details');\n      }\n\n      const data = await response.json();\n      const project = data.project;\n\n      // Debug: Log the raw project data to check YouTube URLs\n      console.log('Raw project data:', project);\n\n      // Check if variables and resources exist\n      if (project.variables) {\n        project.variables.forEach((variable: any, vIndex: number) => {\n          console.log(`Variable ${vIndex + 1} (${variable.name}):`);\n          if (variable.resources) {\n            variable.resources.forEach((resource: any, rIndex: number) => {\n              console.log(`  Resource ${rIndex + 1} - YouTube URL:`, resource.youtube_url);\n            });\n          }\n        });\n      }\n\n      // Format the project data for the edit form\n      const formattedProject = {\n        id: project.id,\n        name: project.name,\n        description: project.description || '',\n        members: project.members || 1,\n        variables: project.variables.map((variable: any) => ({\n          id: variable.id,\n          name: variable.name,\n          note: variable.note || '',\n          resources: variable.resources.map((resource: any) => {\n            // Debug log for each resource\n            console.log(`Processing resource:`, {\n              id: resource.id,\n              google_doc_link: resource.google_doc_link,\n              youtube_url: resource.youtube_url,\n              youtube_url_from_note: resource.youtube_url_from_note,\n              from_video_table: resource.from_video_table,\n              video_id: resource.video_id\n            });\n\n            return {\n              id: resource.id,\n              googleDocLink: resource.google_doc_link || '',\n              youtubeUrl: resource.youtube_url || '',\n              note: resource.note || '',\n              account: resource.account || '',\n              // Preserve special flags for resources from the video table\n              from_video_table: resource.from_video_table || false,\n              video_id: resource.video_id || null,\n              trello_card_id: resource.trello_card_id || null\n            };\n          })\n        })),\n        deletedVariableIds: [],\n        deletedResourceIds: []\n      };\n\n      // Debug: Log the formatted project data to verify YouTube URLs are mapped correctly\n      console.log('Formatted project data:', formattedProject);\n\n      // Debug: Check specifically for YouTube URLs in the formatted data\n      if (formattedProject.variables) {\n        formattedProject.variables.forEach((variable: any, vIndex: number) => {\n          console.log(`Formatted Variable ${vIndex + 1} (${variable.name}):`);\n          if (variable.resources) {\n            variable.resources.forEach((resource: any, rIndex: number) => {\n              console.log(`  Resource ${rIndex + 1} - YouTube URL:`, resource.youtubeUrl);\n            });\n          }\n        });\n      }\n\n      setEditProject(formattedProject);\n      setShowEditProjectModal(true);\n    } catch (error) {\n      console.error('Error fetching project for editing:', error);\n      alert('Failed to load project for editing. Please try again.');\n    }\n  };\n\n  // Add a new variable to the edit project\n  const addEditVariable = () => {\n    if (!editProject) return;\n\n    setEditProject({\n      ...editProject,\n      variables: [\n        ...editProject.variables,\n        {\n          name: '',\n          note: '',\n          resources: [\n            {\n              googleDocLink: '',\n              note: '',\n              youtubeUrl: ''\n            }\n          ]\n        }\n      ]\n    });\n  };\n\n  // Add a new resource to a variable in edit project\n  const addEditResource = (variableIndex: number) => {\n    if (!editProject) return;\n\n    const updatedVariables = [...editProject.variables];\n    updatedVariables[variableIndex].resources.push({\n      googleDocLink: '',\n      note: '',\n      youtubeUrl: '',\n      account: ''\n    });\n\n    setEditProject({\n      ...editProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Remove a variable from edit project\n  const removeEditVariable = (index: number) => {\n    if (!editProject) return;\n\n    const variable = editProject.variables[index];\n    const updatedVariables = editProject.variables.filter((_: any, i: number) => i !== index);\n\n    // If the variable has an ID, add it to deletedVariableIds\n    const updatedDeletedVariableIds = [...editProject.deletedVariableIds];\n    if (variable.id) {\n      updatedDeletedVariableIds.push(variable.id);\n    }\n\n    setEditProject({\n      ...editProject,\n      variables: updatedVariables,\n      deletedVariableIds: updatedDeletedVariableIds\n    });\n  };\n\n  // Remove a resource from edit project\n  const removeEditResource = (variableIndex: number, resourceIndex: number) => {\n    if (!editProject) return;\n\n    const updatedVariables = [...editProject.variables];\n    const resource = updatedVariables[variableIndex].resources[resourceIndex];\n\n    updatedVariables[variableIndex].resources = updatedVariables[variableIndex].resources.filter((_: any, i: number) => i !== resourceIndex);\n\n    // If the resource has an ID, track it for deletion\n    let updatedDeletedResourceIds = [...(editProject.variables[variableIndex].deletedResourceIds || [])];\n    if (resource.id) {\n      if (!updatedDeletedResourceIds) {\n        updatedDeletedResourceIds = [];\n      }\n      updatedDeletedResourceIds.push(resource.id);\n    }\n\n    // Update the variable with the new deletedResourceIds\n    updatedVariables[variableIndex] = {\n      ...updatedVariables[variableIndex],\n      deletedResourceIds: updatedDeletedResourceIds\n    };\n\n    setEditProject({\n      ...editProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Update variable field in edit project\n  const updateEditVariableField = (variableIndex: number, field: string, value: string) => {\n    if (!editProject) return;\n\n    const updatedVariables = [...editProject.variables];\n    updatedVariables[variableIndex] = {\n      ...updatedVariables[variableIndex],\n      [field]: value\n    };\n\n    setEditProject({\n      ...editProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Update resource field in edit project\n  const updateEditResourceField = (variableIndex: number, resourceIndex: number, field: string, value: string) => {\n    if (!editProject) return;\n\n    // Debug: Log the current value and the new value\n    console.log(`Updating ${field} for variable ${variableIndex}, resource ${resourceIndex}`);\n    console.log(`  Current value:`, editProject.variables[variableIndex].resources[resourceIndex][field]);\n    console.log(`  New value:`, value);\n\n    const updatedVariables = [...editProject.variables];\n    updatedVariables[variableIndex].resources[resourceIndex] = {\n      ...updatedVariables[variableIndex].resources[resourceIndex],\n      [field]: value\n    };\n\n    // Debug: Log the updated resource\n    console.log(`  Updated resource:`, updatedVariables[variableIndex].resources[resourceIndex]);\n\n    setEditProject({\n      ...editProject,\n      variables: updatedVariables\n    });\n  };\n\n  // Handle saving the edited project\n  const handleUpdateProject = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!editProject) return;\n\n    setEditProjectLoading(true);\n    setEditProjectError('');\n\n    try {\n      // Call the API to update the project\n      const response = await fetch(`/api/projects/${editProject.id}`, {\n        method: 'PUT',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(editProject),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        throw new Error(errorData.error || 'Failed to update project');\n      }\n\n      // Refresh the projects list\n      const projectsResponse = await fetch('/api/projects');\n      if (projectsResponse.ok) {\n        const data = await projectsResponse.json();\n\n        // Transform the data to match our expected format\n        const formattedProjects = data.projects.map((p: any) => ({\n          id: p.id,\n          name: p.name,\n          tasks: parseInt(p.variable_count) || 0,\n          members: p.members || 1,\n          dueDate: new Date(p.created_at).toISOString().split('T')[0]\n        }));\n\n        setProjects(formattedProjects);\n      }\n\n      // Close the modal\n      setShowEditProjectModal(false);\n      setEditProject(null);\n    } catch (error: any) {\n      setEditProjectError(error.message);\n    } finally {\n      setEditProjectLoading(false);\n    }\n  };\n\n  const handleCreateProject = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setCreateProjectLoading(true);\n    setCreateProjectError('');\n\n    try {\n      // For development/demo purposes, just add to local state if API fails\n      try {\n        // Call the API to save the project\n        const response = await fetch('/api/ab-projects', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(newProject),\n        });\n\n        if (!response.ok) {\n          const errorData = await response.json();\n          throw new Error(errorData.error || 'Failed to create project');\n        }\n\n        const data = await response.json();\n\n        // Get current date in YYYY-MM-DD format\n        const currentDate = new Date().toISOString().split('T')[0];\n\n        // Create the new project object\n        const newProjectObj = {\n          id: data.projectId,\n          name: newProject.name,\n          tasks: newProject.variables.length,\n          members: 1,\n          dueDate: currentDate,\n          variables: newProject.variables // Save the actual variables\n        };\n\n        // Add to local state\n        const updatedProjects = [...projects, newProjectObj];\n        setProjects(updatedProjects);\n\n        // Save to localStorage\n        localStorage.setItem('projects', JSON.stringify(updatedProjects));\n      } catch (apiError: any) {\n        console.error('API error:', apiError);\n        // Fallback to local state only\n        const newId = projects.length > 0 ? Math.max(...projects.map(p => p.id)) + 1 : 1;\n\n        // Get current date in YYYY-MM-DD format\n        const currentDate = new Date().toISOString().split('T')[0];\n\n        // Create the new project object\n        const newProjectObj = {\n          id: newId,\n          name: newProject.name,\n          tasks: newProject.variables.length,\n          members: 1,\n          dueDate: currentDate,\n          variables: newProject.variables // Save the actual variables\n        };\n\n        // Add to local state\n        const updatedProjects = [...projects, newProjectObj];\n        setProjects(updatedProjects);\n\n        // Save to localStorage\n        localStorage.setItem('projects', JSON.stringify(updatedProjects));\n      }\n\n      // Reset form\n      setNewProject({\n        name: '',\n        description: '',\n        variables: [\n          {\n            name: '',\n            note: '',\n            resources: [\n              {\n                googleDocLink: '',\n                note: '',\n                youtubeUrl: '',\n                account: ''\n              }\n            ]\n          }\n        ]\n      });\n\n      setShowNewProjectModal(false);\n    } catch (error: any) {\n      setCreateProjectError(error.message);\n    } finally {\n      setCreateProjectLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      <h1 className=\"text-2xl font-bold text-gray-800\">Welcome to your Project Dashboard</h1>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Create New Project Block */}\n        <div className=\"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n          <div className=\"bg-gradient-to-r from-blue-500 to-indigo-600 p-4\">\n            <h2 className=\"text-xl font-semibold text-white\">Create New Project</h2>\n          </div>\n\n          <div className=\"p-6\">\n            <p className=\"text-gray-600 mb-6\">\n              Start a new project and track your progress from start to finish.\n            </p>\n\n            <button\n              onClick={() => setShowNewProjectModal(true)}\n              className=\"w-full flex items-center justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n            >\n              <FaPlus className=\"mr-2\" /> Create New Project\n            </button>\n          </div>\n        </div>\n\n        {/* Existing Projects Block */}\n        <div className=\"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow\">\n          <div className=\"bg-gradient-to-r from-green-500 to-teal-600 p-4\">\n            <h2 className=\"text-xl font-semibold text-white\">Your Projects</h2>\n          </div>\n\n          <div className=\"p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <p className=\"text-gray-600\">\n                Select an existing project to view or edit.\n              </p>\n              {projects.length > 0 && (\n                <button\n                  onClick={clearAllProjects}\n                  className=\"text-xs text-red-500 hover:text-red-700\"\n                  type=\"button\"\n                >\n                  Clear All\n                </button>\n              )}\n            </div>\n\n            <div className=\"space-y-3 max-h-64 overflow-y-auto\">\n              {projects.length > 0 ? (\n                projects.map(project => (\n                  <div\n                    key={project.id}\n                    className=\"flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\"\n                    onClick={() => router.push(`/dashboard/projects/${project.id}`)}\n                  >\n                    <div className=\"text-blue-500 mr-3\">\n                      <FaFolder />\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-medium text-gray-900\">{project.name}</h3>\n                      <div className=\"flex items-center text-sm text-gray-500 mt-1 space-x-4\">\n                        <span className=\"flex items-center\">\n                          <FaTasks className=\"mr-1\" /> {project.tasks} variables\n                        </span>\n                        <span className=\"flex items-center\">\n                          <FaUsers className=\"mr-1\" /> {project.members} members\n                        </span>\n                        <span className=\"flex items-center\">\n                          <FaCalendarAlt className=\"mr-1\" /> {project.dueDate}\n                        </span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3\">\n                      <button\n                        onClick={(e) => handleEditProject(e, project.id)}\n                        className=\"text-gray-400 hover:text-yellow-500 transition-colors\"\n                        title=\"Edit Project\"\n                      >\n                        <FaPencilAlt />\n                      </button>\n                      <div className=\"text-gray-400 hover:text-blue-500\">\n                        <FaFolderOpen />\n                      </div>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <div className=\"text-center py-4 text-gray-500\">\n                  No projects yet. Create your first project!\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* New Project Modal */}\n      {showNewProjectModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto\">\n          <div className=\"bg-white rounded-xl shadow-xl w-full max-w-4xl my-8\">\n            <div className=\"bg-gradient-to-r from-blue-500 to-indigo-600 p-4 rounded-t-xl\">\n              <h2 className=\"text-xl font-semibold text-white\">Create New Project</h2>\n            </div>\n\n            <form onSubmit={handleCreateProject} className=\"p-6 space-y-6 max-h-[80vh] overflow-y-auto\">\n              {createProjectError && (\n                <div className=\"bg-red-50 text-red-500 p-3 rounded-lg text-sm mb-4\">\n                  {createProjectError}\n                </div>\n              )}\n\n              {/* Basic Project Information */}\n              <div>\n                <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Project Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"name\"\n                  value={newProject.name}\n                  onChange={(e) => setNewProject({...newProject, name: e.target.value})}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n                  required\n                />\n              </div>\n\n              <div className=\"text-sm text-gray-500\">\n                <span>Creation Date: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}</span>\n              </div>\n\n              <div>\n                <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  id=\"description\"\n                  value={newProject.description}\n                  onChange={(e) => setNewProject({...newProject, description: e.target.value})}\n                  rows={2}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n                />\n              </div>\n\n\n\n              {/* Variables Section */}\n              <div className=\"border-t border-gray-200 pt-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Project Variables</h3>\n\n                {newProject.variables.map((variable, variableIndex) => (\n                  <div key={variableIndex} className=\"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n                    <div className=\"flex justify-between items-center mb-3\">\n                      <h4 className=\"text-md font-medium text-gray-800\">Variable {variableIndex + 1}</h4>\n                      {variableIndex > 0 && (\n                        <button\n                          type=\"button\"\n                          onClick={() => removeVariable(variableIndex)}\n                          className=\"text-red-500 hover:text-red-700\"\n                        >\n                          <FaTrash />\n                        </button>\n                      )}\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          Variable Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={variable.name}\n                          onChange={(e) => updateVariableField(variableIndex, 'name', e.target.value)}\n                          className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n                          placeholder=\"Enter variable name\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          Variable Note\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={variable.note}\n                          onChange={(e) => updateVariableField(variableIndex, 'note', e.target.value)}\n                          className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n                          placeholder=\"Add a note about this variable\"\n                        />\n                      </div>\n                    </div>\n\n                    {/* Resources Section */}\n                    <div className=\"mt-4\">\n                      <h5 className=\"text-sm font-medium text-gray-700 mb-2\">Resources</h5>\n\n                      {variable.resources.map((resource, resourceIndex) => (\n                        <div key={resourceIndex} className=\"p-3 border border-gray-200 rounded-md mb-3 bg-white\">\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <h6 className=\"text-sm font-medium text-gray-700\">Resource {resourceIndex + 1}</h6>\n                            <button\n                              type=\"button\"\n                              onClick={() => removeResource(variableIndex, resourceIndex)}\n                              className=\"text-red-500 hover:text-red-700 text-sm\"\n                            >\n                              <FaTrash />\n                            </button>\n                          </div>\n\n                          <div className=\"space-y-3\">\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaLink className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <input\n                                type=\"url\"\n                                value={resource.googleDocLink}\n                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'googleDocLink', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black\"\n                                placeholder=\"Google Doc Link\"\n                              />\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaFileAlt className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <input\n                                type=\"text\"\n                                value={resource.note}\n                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'note', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black\"\n                                placeholder=\"Resource Note\"\n                              />\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaYoutube className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <input\n                                type=\"url\"\n                                value={resource.youtubeUrl}\n                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'youtubeUrl', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black\"\n                                placeholder=\"YouTube URL\"\n                              />\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaUserAlt className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <select\n                                value={resource.account}\n                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'account', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black\"\n                              >\n                                {accounts.map(account => (\n                                  <option key={account.id} value={account.account}>{account.account}</option>\n                                ))}\n                              </select>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n\n                      <button\n                        type=\"button\"\n                        onClick={() => addResource(variableIndex)}\n                        className=\"mt-2 flex items-center text-sm text-indigo-600 hover:text-indigo-800\"\n                      >\n                        <FaPlus className=\"mr-1\" /> Add Another Resource\n                      </button>\n                    </div>\n                  </div>\n                ))}\n\n                <button\n                  type=\"button\"\n                  onClick={addVariable}\n                  className=\"mt-2 flex items-center text-indigo-600 hover:text-indigo-800\"\n                >\n                  <FaPlus className=\"mr-1\" /> Add Another Variable\n                </button>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n                <button\n                  type=\"button\"\n                  onClick={() => setShowNewProjectModal(false)}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={createProjectLoading}\n                  className=\"px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center justify-center min-w-[120px]\"\n                >\n                  {createProjectLoading ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Saving...\n                    </>\n                  ) : (\n                    'Create Project'\n                  )}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n\n      {/* Edit Project Modal */}\n      {showEditProjectModal && editProject && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto\">\n          <div className=\"bg-white rounded-xl shadow-xl w-full max-w-4xl my-8\">\n            <div className=\"bg-gradient-to-r from-yellow-500 to-amber-600 p-4 rounded-t-xl\">\n              <h2 className=\"text-xl font-semibold text-white\">Edit Project: {editProject.name}</h2>\n            </div>\n\n            <form onSubmit={handleUpdateProject} className=\"p-6 space-y-6 max-h-[80vh] overflow-y-auto\">\n              {editProjectError && (\n                <div className=\"bg-red-50 text-red-500 p-3 rounded-lg text-sm mb-4\">\n                  {editProjectError}\n                </div>\n              )}\n\n              {/* Basic Project Information */}\n              <div>\n                <label htmlFor=\"edit-name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Project Name\n                </label>\n                <input\n                  type=\"text\"\n                  id=\"edit-name\"\n                  value={editProject.name}\n                  onChange={(e) => setEditProject({...editProject, name: e.target.value})}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black\"\n                  required\n                />\n              </div>\n\n              <div>\n                <label htmlFor=\"edit-description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Description\n                </label>\n                <textarea\n                  id=\"edit-description\"\n                  value={editProject.description}\n                  onChange={(e) => setEditProject({...editProject, description: e.target.value})}\n                  rows={2}\n                  className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black\"\n                />\n              </div>\n\n\n\n              {/* Variables Section */}\n              <div className=\"border-t border-gray-200 pt-4\">\n                <h3 className=\"text-lg font-medium text-gray-900 mb-4\">Project Variables</h3>\n\n                {editProject.variables.map((variable: any, variableIndex: number) => (\n                  <div key={variableIndex} className=\"mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50\">\n                    <div className=\"flex justify-between items-center mb-3\">\n                      <h4 className=\"text-md font-medium text-gray-800\">Variable {variableIndex + 1}</h4>\n                      <button\n                        type=\"button\"\n                        onClick={() => removeEditVariable(variableIndex)}\n                        className=\"text-red-500 hover:text-red-700\"\n                      >\n                        <FaTrash />\n                      </button>\n                    </div>\n\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          Variable Name\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={variable.name}\n                          onChange={(e) => updateEditVariableField(variableIndex, 'name', e.target.value)}\n                          className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black\"\n                          placeholder=\"Enter variable name\"\n                        />\n                      </div>\n\n                      <div>\n                        <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                          Variable Note\n                        </label>\n                        <input\n                          type=\"text\"\n                          value={variable.note}\n                          onChange={(e) => updateEditVariableField(variableIndex, 'note', e.target.value)}\n                          className=\"block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black\"\n                          placeholder=\"Add a note about this variable\"\n                        />\n                      </div>\n                    </div>\n\n                    {/* Resources Section */}\n                    <div className=\"mt-4\">\n                      <h5 className=\"text-sm font-medium text-gray-700 mb-2\">Resources</h5>\n\n                      {variable.resources.map((resource: any, resourceIndex: number) => (\n                        <div key={resourceIndex} className=\"p-3 border border-gray-200 rounded-md mb-3 bg-white\">\n                          <div className=\"flex justify-between items-center mb-2\">\n                            <h6 className=\"text-sm font-medium text-gray-700\">Resource {resourceIndex + 1}</h6>\n                            <button\n                              type=\"button\"\n                              onClick={() => removeEditResource(variableIndex, resourceIndex)}\n                              className=\"text-red-500 hover:text-red-700 text-sm\"\n                            >\n                              <FaTrash />\n                            </button>\n                          </div>\n\n                          <div className=\"space-y-3\">\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaLink className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <input\n                                type=\"url\"\n                                value={resource.googleDocLink}\n                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'googleDocLink', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black\"\n                                placeholder=\"Google Doc Link\"\n                              />\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaFileAlt className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <input\n                                type=\"text\"\n                                value={resource.note}\n                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'note', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black\"\n                                placeholder=\"Resource Note\"\n                              />\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaYoutube className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              {/* Add a data attribute to help debug the value */}\n                              <input\n                                type=\"url\"\n                                value={resource.youtubeUrl || ''}\n                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'youtubeUrl', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black\"\n                                placeholder=\"YouTube URL\"\n                                data-youtube-url={resource.youtubeUrl || ''}\n                                data-resource-id={resource.id || ''}\n                              />\n                              {/* Add a debug element to show the value */}\n                              {process.env.NODE_ENV === 'development' && (\n                                <div className=\"text-xs text-gray-500 mt-1\">\n                                  Debug - YouTube URL: {resource.youtubeUrl || 'empty'}\n                                </div>\n                              )}\n                            </div>\n\n                            <div className=\"relative\">\n                              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                                <FaUserAlt className=\"h-4 w-4 text-gray-400\" />\n                              </div>\n                              <select\n                                value={resource.account}\n                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'account', e.target.value)}\n                                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black\"\n                              >\n                                <option value=\"Auto Assign\">Auto Assign</option>\n                                {accounts.filter(account => account.account !== 'Auto Assign').map(account => (\n                                  <option key={account.id} value={account.account}>{account.account}</option>\n                                ))}\n                              </select>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n\n                      <button\n                        type=\"button\"\n                        onClick={() => addEditResource(variableIndex)}\n                        className=\"mt-2 flex items-center text-sm text-amber-600 hover:text-amber-800\"\n                      >\n                        <FaPlus className=\"mr-1\" /> Add Another Resource\n                      </button>\n                    </div>\n                  </div>\n                ))}\n\n                <button\n                  type=\"button\"\n                  onClick={addEditVariable}\n                  className=\"mt-2 flex items-center text-amber-600 hover:text-amber-800\"\n                >\n                  <FaPlus className=\"mr-1\" /> Add Another Variable\n                </button>\n              </div>\n\n              <div className=\"flex justify-end space-x-3 pt-4 border-t border-gray-200\">\n                <button\n                  type=\"button\"\n                  onClick={() => {\n                    setShowEditProjectModal(false);\n                    setEditProject(null);\n                  }}\n                  className=\"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                >\n                  Cancel\n                </button>\n                <button\n                  type=\"submit\"\n                  disabled={editProjectLoading}\n                  className=\"px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 flex items-center justify-center min-w-[120px]\"\n                >\n                  {editProjectLoading ? (\n                    <>\n                      <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                      </svg>\n                      Saving...\n                    </>\n                  ) : (\n                    'Save Changes'\n                  )}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAMnC,EAAE;IAEN,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC,EAAE;IAC5E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,MAAM;QACN,aAAa;QACb,WAAW;YACT;gBACE,MAAM;gBACN,MAAM;gBACN,WAAW;oBACT;wBACE,eAAe;wBACf,MAAM;wBACN,YAAY;wBACZ,SAAS,cAAe,6BAA6B;oBACvD;iBACD;YACH;SACD;IACH;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,4BAA4B;gBAC5B,MAAM,WAAW,MAAM,MAAM;gBAE7B,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,kDAAkD;oBAClD,MAAM,oBAAoB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAW,CAAC;4BACvD,IAAI,EAAE,EAAE;4BACR,MAAM,EAAE,IAAI;4BACZ,OAAO,SAAS,EAAE,cAAc,KAAK;4BACrC,SAAS,EAAE,OAAO,IAAI;4BACtB,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;wBAC7D,CAAC;oBAED,YAAY;gBACd,OAAO;oBACL,0DAA0D;oBAC1D,MAAM,gBAAgB,aAAa,OAAO,CAAC;oBAC3C,IAAI,eAAe;wBACjB,IAAI;4BACF,YAAY,KAAK,KAAK,CAAC;wBACzB,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,iCAAiC;wBACjD;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAE1C,2BAA2B;gBAC3B,MAAM,gBAAgB,aAAa,OAAO,CAAC;gBAC3C,IAAI,eAAe;oBACjB,IAAI;wBACF,YAAY,KAAK,KAAK,CAAC;oBACzB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,iCAAiC;oBACjD;gBACF;YACF;QACF;QAEA;IACF,GAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,IAAI;gBACF,0CAA0C;gBAC1C,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW,MAAM,MAAM;oBAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,QAAQ,GAAG,CAAC,qDAAqD;oBACjE,wCAAwC;oBACxC,YAAY;wBAAC;4BAAE,IAAI;4BAAG,SAAS;wBAAc;2BAAM,KAAK,QAAQ;qBAAC;oBACjE,QAAQ,qBAAqB;gBAC/B,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,qDAAqD;gBACvD;gBAEA,mCAAmC;gBACnC,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,MAAM,iBAAiB,MAAM,MAAM;oBAEnC,IAAI,CAAC,eAAe,EAAE,EAAE;wBACtB,MAAM,IAAI,MAAM;oBAClB;oBAEA,MAAM,aAAa,MAAM,eAAe,IAAI;oBAC5C,QAAQ,GAAG,CAAC,4DAA4D;oBACxE,wCAAwC;oBACxC,YAAY;wBAAC;4BAAE,IAAI;4BAAG,SAAS;wBAAc;2BAAM,WAAW,QAAQ;qBAAC;oBACvE,QAAQ,qBAAqB;gBAC/B,EAAE,OAAO,gBAAgB;oBACvB,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,mDAAmD;gBACrD;gBAEA,iDAAiD;gBACjD,IAAI;oBACF,QAAQ,GAAG,CAAC;oBACZ,MAAM,gBAAgB,MAAM,MAAM;oBAElC,IAAI,cAAc,EAAE,EAAE;wBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;wBAC1C,QAAQ,GAAG,CAAC,sBAAsB;oBACpC;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,oBAAoB;gBACpC;gBAEA,uCAAuC;gBACvC,QAAQ,GAAG,CAAC;gBACZ,YAAY;oBACV;wBAAE,IAAI;wBAAG,SAAS;oBAAc;oBAChC;wBAAE,IAAI;wBAAG,SAAS;oBAAW;oBAC7B;wBAAE,IAAI;wBAAG,SAAS;oBAAW;oBAC7B;wBAAE,IAAI;wBAAG,SAAS;oBAAW;iBAC9B;YACH,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,sCAAsC;YACtD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,oCAAoC;IACpC,MAAM,cAAc;QAClB,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;mBACN,WAAW,SAAS;gBACvB;oBACE,MAAM;oBACN,MAAM;oBACN,WAAW;wBACT;4BACE,eAAe;4BACf,MAAM;4BACN,YAAY;wBACd;qBACD;gBACH;aACD;QACH;IACF;IAEA,mCAAmC;IACnC,MAAM,cAAc,CAAC;QACnB,MAAM,mBAAmB;eAAI,WAAW,SAAS;SAAC;QAClD,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC;YAC7C,eAAe;YACf,MAAM;YACN,YAAY;YACZ,SAAS,cAAe,6BAA6B;QACvD;QAEA,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,CAAC;QACtB,MAAM,mBAAmB,WAAW,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrE,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;QACb;IACF;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,CAAC,eAAuB;QAC7C,MAAM,mBAAmB;eAAI,WAAW,SAAS;SAAC;QAClD,gBAAgB,CAAC,cAAc,CAAC,SAAS,GAAG,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAE7G,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC,eAAuB,OAAe;QACjE,MAAM,mBAAmB;eAAI,WAAW,SAAS;SAAC;QAClD,gBAAgB,CAAC,cAAc,GAAG;YAChC,GAAG,gBAAgB,CAAC,cAAc;YAClC,CAAC,MAAM,EAAE;QACX;QAEA,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB,CAAC,eAAuB,eAAuB,OAAe;QACxF,MAAM,mBAAmB;eAAI,WAAW,SAAS;SAAC;QAClD,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG;YACzD,GAAG,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;YAC3D,CAAC,MAAM,EAAE;QACX;QAEA,cAAc;YACZ,GAAG,UAAU;YACb,WAAW;QACb;IACF;IAEA,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBAAqB;IACrB,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,+CAA+C;IAC/C,MAAM,mBAAmB;QACvB,YAAY,EAAE;QACd,aAAa,UAAU,CAAC;IAC1B;IAEA,4CAA4C;IAC5C,MAAM,oBAAoB,OAAO,GAAqB;QACpD,EAAE,eAAe,IAAI,4CAA4C;QAEjE,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YAEzD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,UAAU,KAAK,OAAO;YAE5B,wDAAwD;YACxD,QAAQ,GAAG,CAAC,qBAAqB;YAEjC,yCAAyC;YACzC,IAAI,QAAQ,SAAS,EAAE;gBACrB,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,UAAe;oBACxC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC;oBACxD,IAAI,SAAS,SAAS,EAAE;wBACtB,SAAS,SAAS,CAAC,OAAO,CAAC,CAAC,UAAe;4BACzC,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC,EAAE,SAAS,WAAW;wBAC7E;oBACF;gBACF;YACF;YAEA,4CAA4C;YAC5C,MAAM,mBAAmB;gBACvB,IAAI,QAAQ,EAAE;gBACd,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,WAAW,IAAI;gBACpC,SAAS,QAAQ,OAAO,IAAI;gBAC5B,WAAW,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC,WAAkB,CAAC;wBACnD,IAAI,SAAS,EAAE;wBACf,MAAM,SAAS,IAAI;wBACnB,MAAM,SAAS,IAAI,IAAI;wBACvB,WAAW,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC;4BACjC,8BAA8B;4BAC9B,QAAQ,GAAG,CAAC,CAAC,oBAAoB,CAAC,EAAE;gCAClC,IAAI,SAAS,EAAE;gCACf,iBAAiB,SAAS,eAAe;gCACzC,aAAa,SAAS,WAAW;gCACjC,uBAAuB,SAAS,qBAAqB;gCACrD,kBAAkB,SAAS,gBAAgB;gCAC3C,UAAU,SAAS,QAAQ;4BAC7B;4BAEA,OAAO;gCACL,IAAI,SAAS,EAAE;gCACf,eAAe,SAAS,eAAe,IAAI;gCAC3C,YAAY,SAAS,WAAW,IAAI;gCACpC,MAAM,SAAS,IAAI,IAAI;gCACvB,SAAS,SAAS,OAAO,IAAI;gCAC7B,4DAA4D;gCAC5D,kBAAkB,SAAS,gBAAgB,IAAI;gCAC/C,UAAU,SAAS,QAAQ,IAAI;gCAC/B,gBAAgB,SAAS,cAAc,IAAI;4BAC7C;wBACF;oBACF,CAAC;gBACD,oBAAoB,EAAE;gBACtB,oBAAoB,EAAE;YACxB;YAEA,oFAAoF;YACpF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,mEAAmE;YACnE,IAAI,iBAAiB,SAAS,EAAE;gBAC9B,iBAAiB,SAAS,CAAC,OAAO,CAAC,CAAC,UAAe;oBACjD,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,IAAI,CAAC,EAAE,CAAC;oBAClE,IAAI,SAAS,SAAS,EAAE;wBACtB,SAAS,SAAS,CAAC,OAAO,CAAC,CAAC,UAAe;4BACzC,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,SAAS,EAAE,eAAe,CAAC,EAAE,SAAS,UAAU;wBAC5E;oBACF;gBACF;YACF;YAEA,eAAe;YACf,wBAAwB;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,kBAAkB;QACtB,IAAI,CAAC,aAAa;QAElB,eAAe;YACb,GAAG,WAAW;YACd,WAAW;mBACN,YAAY,SAAS;gBACxB;oBACE,MAAM;oBACN,MAAM;oBACN,WAAW;wBACT;4BACE,eAAe;4BACf,MAAM;4BACN,YAAY;wBACd;qBACD;gBACH;aACD;QACH;IACF;IAEA,mDAAmD;IACnD,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,aAAa;QAElB,MAAM,mBAAmB;eAAI,YAAY,SAAS;SAAC;QACnD,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC;YAC7C,eAAe;YACf,MAAM;YACN,YAAY;YACZ,SAAS;QACX;QAEA,eAAe;YACb,GAAG,WAAW;YACd,WAAW;QACb;IACF;IAEA,sCAAsC;IACtC,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,aAAa;QAElB,MAAM,WAAW,YAAY,SAAS,CAAC,MAAM;QAC7C,MAAM,mBAAmB,YAAY,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,IAAc,MAAM;QAEnF,0DAA0D;QAC1D,MAAM,4BAA4B;eAAI,YAAY,kBAAkB;SAAC;QACrE,IAAI,SAAS,EAAE,EAAE;YACf,0BAA0B,IAAI,CAAC,SAAS,EAAE;QAC5C;QAEA,eAAe;YACb,GAAG,WAAW;YACd,WAAW;YACX,oBAAoB;QACtB;IACF;IAEA,sCAAsC;IACtC,MAAM,qBAAqB,CAAC,eAAuB;QACjD,IAAI,CAAC,aAAa;QAElB,MAAM,mBAAmB;eAAI,YAAY,SAAS;SAAC;QACnD,MAAM,WAAW,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;QAEzE,gBAAgB,CAAC,cAAc,CAAC,SAAS,GAAG,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAQ,IAAc,MAAM;QAE1H,mDAAmD;QACnD,IAAI,4BAA4B;eAAK,YAAY,SAAS,CAAC,cAAc,CAAC,kBAAkB,IAAI,EAAE;SAAE;QACpG,IAAI,SAAS,EAAE,EAAE;YACf,IAAI,CAAC,2BAA2B;gBAC9B,4BAA4B,EAAE;YAChC;YACA,0BAA0B,IAAI,CAAC,SAAS,EAAE;QAC5C;QAEA,sDAAsD;QACtD,gBAAgB,CAAC,cAAc,GAAG;YAChC,GAAG,gBAAgB,CAAC,cAAc;YAClC,oBAAoB;QACtB;QAEA,eAAe;YACb,GAAG,WAAW;YACd,WAAW;QACb;IACF;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,CAAC,eAAuB,OAAe;QACrE,IAAI,CAAC,aAAa;QAElB,MAAM,mBAAmB;eAAI,YAAY,SAAS;SAAC;QACnD,gBAAgB,CAAC,cAAc,GAAG;YAChC,GAAG,gBAAgB,CAAC,cAAc;YAClC,CAAC,MAAM,EAAE;QACX;QAEA,eAAe;YACb,GAAG,WAAW;YACd,WAAW;QACb;IACF;IAEA,wCAAwC;IACxC,MAAM,0BAA0B,CAAC,eAAuB,eAAuB,OAAe;QAC5F,IAAI,CAAC,aAAa;QAElB,iDAAiD;QACjD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,cAAc,EAAE,cAAc,WAAW,EAAE,eAAe;QACxF,QAAQ,GAAG,CAAC,CAAC,gBAAgB,CAAC,EAAE,YAAY,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM;QACpG,QAAQ,GAAG,CAAC,CAAC,YAAY,CAAC,EAAE;QAE5B,MAAM,mBAAmB;eAAI,YAAY,SAAS;SAAC;QACnD,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,GAAG;YACzD,GAAG,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;YAC3D,CAAC,MAAM,EAAE;QACX;QAEA,kCAAkC;QAClC,QAAQ,GAAG,CAAC,CAAC,mBAAmB,CAAC,EAAE,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc;QAE3F,eAAe;YACb,GAAG,WAAW;YACd,WAAW;QACb;IACF;IAEA,mCAAmC;IACnC,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,IAAI,CAAC,aAAa;QAElB,sBAAsB;QACtB,oBAAoB;QAEpB,IAAI;YACF,qCAAqC;YACrC,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,YAAY,EAAE,EAAE,EAAE;gBAC9D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;YAEA,4BAA4B;YAC5B,MAAM,mBAAmB,MAAM,MAAM;YACrC,IAAI,iBAAiB,EAAE,EAAE;gBACvB,MAAM,OAAO,MAAM,iBAAiB,IAAI;gBAExC,kDAAkD;gBAClD,MAAM,oBAAoB,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAW,CAAC;wBACvD,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,OAAO,SAAS,EAAE,cAAc,KAAK;wBACrC,SAAS,EAAE,OAAO,IAAI;wBACtB,SAAS,IAAI,KAAK,EAAE,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;oBAC7D,CAAC;gBAED,YAAY;YACd;YAEA,kBAAkB;YAClB,wBAAwB;YACxB,eAAe;QACjB,EAAE,OAAO,OAAY;YACnB,oBAAoB,MAAM,OAAO;QACnC,SAAU;YACR,sBAAsB;QACxB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,EAAE,cAAc;QAChB,wBAAwB;QACxB,sBAAsB;QAEtB,IAAI;YACF,sEAAsE;YACtE,IAAI;gBACF,mCAAmC;gBACnC,MAAM,WAAW,MAAM,MAAM,oBAAoB;oBAC/C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;gBACrC;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,wCAAwC;gBACxC,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAE1D,gCAAgC;gBAChC,MAAM,gBAAgB;oBACpB,IAAI,KAAK,SAAS;oBAClB,MAAM,WAAW,IAAI;oBACrB,OAAO,WAAW,SAAS,CAAC,MAAM;oBAClC,SAAS;oBACT,SAAS;oBACT,WAAW,WAAW,SAAS,CAAC,4BAA4B;gBAC9D;gBAEA,qBAAqB;gBACrB,MAAM,kBAAkB;uBAAI;oBAAU;iBAAc;gBACpD,YAAY;gBAEZ,uBAAuB;gBACvB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAClD,EAAE,OAAO,UAAe;gBACtB,QAAQ,KAAK,CAAC,cAAc;gBAC5B,+BAA+B;gBAC/B,MAAM,QAAQ,SAAS,MAAM,GAAG,IAAI,KAAK,GAAG,IAAI,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,IAAI;gBAE/E,wCAAwC;gBACxC,MAAM,cAAc,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAE1D,gCAAgC;gBAChC,MAAM,gBAAgB;oBACpB,IAAI;oBACJ,MAAM,WAAW,IAAI;oBACrB,OAAO,WAAW,SAAS,CAAC,MAAM;oBAClC,SAAS;oBACT,SAAS;oBACT,WAAW,WAAW,SAAS,CAAC,4BAA4B;gBAC9D;gBAEA,qBAAqB;gBACrB,MAAM,kBAAkB;uBAAI;oBAAU;iBAAc;gBACpD,YAAY;gBAEZ,uBAAuB;gBACvB,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAClD;YAEA,aAAa;YACb,cAAc;gBACZ,MAAM;gBACN,aAAa;gBACb,WAAW;oBACT;wBACE,MAAM;wBACN,MAAM;wBACN,WAAW;4BACT;gCACE,eAAe;gCACf,MAAM;gCACN,YAAY;gCACZ,SAAS;4BACX;yBACD;oBACH;iBACD;YACH;YAEA,uBAAuB;QACzB,EAAE,OAAO,OAAY;YACnB,sBAAsB,MAAM,OAAO;QACrC,SAAU;YACR,wBAAwB;QAC1B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAmC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAIlC,8OAAC;wCACC,SAAS,IAAM,uBAAuB;wCACtC,WAAU;;0DAEV,8OAAC,8IAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;kCAMjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;;;;;;0CAGnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;4CAG5B,SAAS,MAAM,GAAG,mBACjB,8OAAC;gDACC,SAAS;gDACT,WAAU;gDACV,MAAK;0DACN;;;;;;;;;;;;kDAML,8OAAC;wCAAI,WAAU;kDACZ,SAAS,MAAM,GAAG,IACjB,SAAS,GAAG,CAAC,CAAA,wBACX,8OAAC;gDAEC,WAAU;gDACV,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,EAAE;;kEAE9D,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;;;;;;;;;;kEAEX,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAA6B,QAAQ,IAAI;;;;;;0EACvD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,8IAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAS;4EAAE,QAAQ,KAAK;4EAAC;;;;;;;kFAE9C,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,8IAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAAS;4EAAE,QAAQ,OAAO;4EAAC;;;;;;;kFAEhD,8OAAC;wEAAK,WAAU;;0FACd,8OAAC,8IAAA,CAAA,gBAAa;gFAAC,WAAU;;;;;;4EAAS;4EAAE,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;kEAIzD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,SAAS,CAAC,IAAM,kBAAkB,GAAG,QAAQ,EAAE;gEAC/C,WAAU;gEACV,OAAM;0EAEN,cAAA,8OAAC,8IAAA,CAAA,cAAW;;;;;;;;;;0EAEd,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC,8IAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;+CA9BZ,QAAQ,EAAE;;;;sEAoCnB,8OAAC;4CAAI,WAAU;sDAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUzD,qCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;sCAGnD,8OAAC;4BAAK,UAAU;4BAAqB,WAAU;;gCAC5C,oCACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAKL,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAA+C;;;;;;sDAG/E,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO,WAAW,IAAI;4CACtB,UAAU,CAAC,IAAM,cAAc;oDAAC,GAAG,UAAU;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAA;4CACnE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;4CAAK;4CAAgB,IAAI,OAAO,kBAAkB;4CAAG;4CAAE,IAAI,OAAO,kBAAkB;;;;;;;;;;;;8CAGvF,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAA+C;;;;;;sDAGtF,8OAAC;4CACC,IAAG;4CACH,OAAO,WAAW,WAAW;4CAC7B,UAAU,CAAC,IAAM,cAAc;oDAAC,GAAG,UAAU;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAA;4CAC1E,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAOd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;wCAEtD,WAAW,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,8BACnC,8OAAC;gDAAwB,WAAU;;kEACjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;;oEAAoC;oEAAU,gBAAgB;;;;;;;4DAC3E,gBAAgB,mBACf,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,eAAe;gEAC9B,WAAU;0EAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kEAKd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,oBAAoB,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC1E,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,oBAAoB,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC1E,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAMlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;4DAEtD,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,8BACjC,8OAAC;oEAAwB,WAAU;;sFACjC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;;wFAAoC;wFAAU,gBAAgB;;;;;;;8FAC5E,8OAAC;oFACC,MAAK;oFACL,SAAS,IAAM,eAAe,eAAe;oFAC7C,WAAU;8FAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sFAIZ,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;;;;;;sGAEpB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,aAAa;4FAC7B,UAAU,CAAC,IAAM,oBAAoB,eAAe,eAAe,iBAAiB,EAAE,MAAM,CAAC,KAAK;4FAClG,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAEvB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,IAAI;4FACpB,UAAU,CAAC,IAAM,oBAAoB,eAAe,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;4FACzF,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAEvB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,UAAU;4FAC1B,UAAU,CAAC,IAAM,oBAAoB,eAAe,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4FAC/F,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAEvB,8OAAC;4FACC,OAAO,SAAS,OAAO;4FACvB,UAAU,CAAC,IAAM,oBAAoB,eAAe,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;4FAC5F,WAAU;sGAET,SAAS,GAAG,CAAC,CAAA,wBACZ,8OAAC;oGAAwB,OAAO,QAAQ,OAAO;8GAAG,QAAQ,OAAO;mGAApD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;mEA9DvB;;;;;0EAsEZ,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,YAAY;gEAC3B,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;+CA1HvB;;;;;sDAgIZ,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,8IAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAS;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,uBAAuB;4CACtC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,qCACC;;kEACE,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;+DAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUb,wBAAwB,6BACvB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;oCAAmC;oCAAe,YAAY,IAAI;;;;;;;;;;;;sCAGlF,8OAAC;4BAAK,UAAU;4BAAqB,WAAU;;gCAC5C,kCACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;8CAKL,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAY,WAAU;sDAA+C;;;;;;sDAGpF,8OAAC;4CACC,MAAK;4CACL,IAAG;4CACH,OAAO,YAAY,IAAI;4CACvB,UAAU,CAAC,IAAM,eAAe;oDAAC,GAAG,WAAW;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAA;4CACrE,WAAU;4CACV,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAmB,WAAU;sDAA+C;;;;;;sDAG3F,8OAAC;4CACC,IAAG;4CACH,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,IAAM,eAAe;oDAAC,GAAG,WAAW;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAA;4CAC5E,MAAM;4CACN,WAAU;;;;;;;;;;;;8CAOd,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;wCAEtD,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,UAAe,8BACzC,8OAAC;gDAAwB,WAAU;;kEACjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;;oEAAoC;oEAAU,gBAAgB;;;;;;;0EAC5E,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,mBAAmB;gEAClC,WAAU;0EAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;kEAIZ,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,wBAAwB,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC9E,WAAU;wEACV,aAAY;;;;;;;;;;;;0EAIhB,8OAAC;;kFACC,8OAAC;wEAAM,WAAU;kFAA+C;;;;;;kFAGhE,8OAAC;wEACC,MAAK;wEACL,OAAO,SAAS,IAAI;wEACpB,UAAU,CAAC,IAAM,wBAAwB,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;wEAC9E,WAAU;wEACV,aAAY;;;;;;;;;;;;;;;;;;kEAMlB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAyC;;;;;;4DAEtD,SAAS,SAAS,CAAC,GAAG,CAAC,CAAC,UAAe,8BACtC,8OAAC;oEAAwB,WAAU;;sFACjC,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAG,WAAU;;wFAAoC;wFAAU,gBAAgB;;;;;;;8FAC5E,8OAAC;oFACC,MAAK;oFACL,SAAS,IAAM,mBAAmB,eAAe;oFACjD,WAAU;8FAEV,cAAA,8OAAC,8IAAA,CAAA,UAAO;;;;;;;;;;;;;;;;sFAIZ,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;;;;;;sGAEpB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,aAAa;4FAC7B,UAAU,CAAC,IAAM,wBAAwB,eAAe,eAAe,iBAAiB,EAAE,MAAM,CAAC,KAAK;4FACtG,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAEvB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,IAAI;4FACpB,UAAU,CAAC,IAAM,wBAAwB,eAAe,eAAe,QAAQ,EAAE,MAAM,CAAC,KAAK;4FAC7F,WAAU;4FACV,aAAY;;;;;;;;;;;;8FAIhB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAGvB,8OAAC;4FACC,MAAK;4FACL,OAAO,SAAS,UAAU,IAAI;4FAC9B,UAAU,CAAC,IAAM,wBAAwB,eAAe,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4FACnG,WAAU;4FACV,aAAY;4FACZ,oBAAkB,SAAS,UAAU,IAAI;4FACzC,oBAAkB,SAAS,EAAE,IAAI;;;;;;wFAGlC,oDAAyB,+BACxB,8OAAC;4FAAI,WAAU;;gGAA6B;gGACpB,SAAS,UAAU,IAAI;;;;;;;;;;;;;8FAKnD,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,8IAAA,CAAA,YAAS;gGAAC,WAAU;;;;;;;;;;;sGAEvB,8OAAC;4FACC,OAAO,SAAS,OAAO;4FACvB,UAAU,CAAC,IAAM,wBAAwB,eAAe,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;4FAChG,WAAU;;8GAEV,8OAAC;oGAAO,OAAM;8GAAc;;;;;;gGAC3B,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK,eAAe,GAAG,CAAC,CAAA,wBACjE,8OAAC;wGAAwB,OAAO,QAAQ,OAAO;kHAAG,QAAQ,OAAO;uGAApD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;mEAxEvB;;;;;0EAgFZ,8OAAC;gEACC,MAAK;gEACL,SAAS,IAAM,gBAAgB;gEAC/B,WAAU;;kFAEV,8OAAC,8IAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAS;;;;;;;;;;;;;;+CAlIvB;;;;;sDAwIZ,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,8IAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAS;;;;;;;;;;;;;8CAI/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS;gDACP,wBAAwB;gDACxB,eAAe;4CACjB;4CACA,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,mCACC;;kEACE,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;+DAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB", "debugId": null}}]}