module.exports = {

"[project]/.next-internal/server/app/api/projects/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/db.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
// Parse the service account credentials from environment variable
let credentials = null;
try {
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
        credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
    }
} catch (error) {
    console.error('Error parsing Google credentials:', error);
}
// Cloud SQL connection configuration
const getPoolConfig = ()=>{
    // Log environment variables for debugging
    console.log('Environment variables:');
    console.log('USE_CLOUD_SQL_AUTH_PROXY:', process.env.USE_CLOUD_SQL_AUTH_PROXY);
    console.log('DB_HOST:', process.env.DB_HOST);
    console.log('DB_PORT:', process.env.DB_PORT);
    console.log('NODE_ENV:', ("TURBOPACK compile-time value", "development"));
    // Force direct connection regardless of environment variable
    // This is a temporary fix to ensure we're using direct connection
    const config = {
        host: '***********',
        database: process.env.DB_NAME || 'postgres',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASS || 'Plotpointe!@3456',
        port: 5432,
        ssl: {
            rejectUnauthorized: false
        } // Enable SSL with rejectUnauthorized: false
    };
    console.log('Using database config:', JSON.stringify(config, null, 2));
    return config;
};
// Create the connection pool
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"](getPoolConfig());
// Add error handler to log connection issues
pool.on('error', (err)=>{
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
});
const __TURBOPACK__default__export__ = pool;
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/app/api/projects/[id]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "DELETE": (()=>DELETE),
    "GET": (()=>GET),
    "PUT": (()=>PUT)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/db.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
([__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
// Helper function to extract YouTube video ID from various URL formats
function extractYoutubeVideoId(url) {
    if (!url) return null;
    try {
        // Handle youtu.be format
        if (url.includes('youtu.be/')) {
            const id = url.split('youtu.be/')[1]?.split(/[?#]/)[0];
            return id || null;
        }
        // Handle youtube.com/shorts format
        if (url.includes('/shorts/')) {
            const id = url.split('/shorts/')[1]?.split(/[?#]/)[0];
            return id || null;
        }
        // Handle youtube.com/watch?v= format
        if (url.includes('/watch?')) {
            const urlObj = new URL(url);
            return urlObj.searchParams.get('v');
        }
        // Handle other potential formats
        const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i;
        const match = url.match(regex);
        return match ? match[1] : null;
    } catch (error) {
        console.error('Error extracting YouTube video ID:', error);
        return null;
    }
}
async function GET(request, { params }) {
    try {
        // Await params before using its properties
        const { id } = await params;
        const projectId = id;
        // Connect to PostgreSQL
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].connect();
        try {
            // Query the database for the project
            const projectResult = await client.query(`SELECT id, name, description, created_at, created_by, members
         FROM src_projects
         WHERE id = $1`, [
                projectId
            ]);
            if (projectResult.rows.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Project not found'
                }, {
                    status: 404
                });
            }
            const project = projectResult.rows[0];
            // Query the database for the project variables
            const variablesResult = await client.query(`SELECT id, name, note, created_at
         FROM src_project_variables
         WHERE project_id = $1
         ORDER BY created_at`, [
                projectId
            ]);
            const variables = variablesResult.rows;
            // For each variable, get its resources
            for (const variable of variables){
                // First, get resources from src_variable_resources table
                const resourcesResult = await client.query(`SELECT id, google_doc_link, youtube_url, note, account, created_at
           FROM src_variable_resources
           WHERE variable_id = $1
           ORDER BY created_at`, [
                    variable.id
                ]);
                // Debug: Log the resources for this variable
                console.log(`Resources for variable ${variable.id} (${variable.name}) from src_variable_resources:`, resourcesResult.rows);
                variable.resources = resourcesResult.rows;
                // For each resource, check if there's a corresponding script entry with a trello_card_id
                for (const resource of variable.resources){
                    if (resource.google_doc_link) {
                        try {
                            // Look for a script entry with this Google Doc link (without variable_id)
                            const scriptResult = await client.query(`SELECT id, trello_card_id, google_doc_link
                 FROM script
                 WHERE google_doc_link = $1`, [
                                resource.google_doc_link
                            ]);
                            console.log(`Script entries for resource ${resource.id} with Google Doc link ${resource.google_doc_link}:`, scriptResult.rows);
                            // If we found a script entry, add the trello_card_id to the resource
                            if (scriptResult.rows.length > 0) {
                                resource.trello_card_id = scriptResult.rows[0].trello_card_id;
                                resource.script_id = scriptResult.rows[0].id;
                                console.log(`Added trello_card_id ${resource.trello_card_id} to resource ${resource.id}`);
                            }
                        } catch (error) {
                            console.error(`Error querying script table for Google Doc link ${resource.google_doc_link}:`, error);
                        }
                        // Also check if there's a YouTube URL in the note field
                        if (resource.note && typeof resource.note === 'string') {
                            // Extract YouTube URL from note if present
                            const youtubeUrlMatch = resource.note.match(/https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/[^\s]+/);
                            if (youtubeUrlMatch) {
                                const youtubeUrl = youtubeUrlMatch[0];
                                console.log(`Found YouTube URL in note for resource ${resource.id}: ${youtubeUrl}`);
                                resource.youtube_url_from_note = youtubeUrl;
                            }
                        }
                    }
                }
                // Now, check if there are any YouTube URLs in the video table for this variable
                const videoResult = await client.query(`SELECT id, url, writer_id, account_id, trello_card_id
           FROM video
           WHERE variable_id = $1 AND url IS NOT NULL AND url != ''`, [
                    variable.id
                ]);
                console.log(`YouTube URLs from video table for variable ${variable.id}:`, videoResult.rows);
                // Also check for videos with trello_card_ids that match our resources
                const trelloCardIds = variable.resources.filter((resource)=>resource.trello_card_id).map((resource)=>resource.trello_card_id);
                if (trelloCardIds.length > 0) {
                    const trelloVideoResult = await client.query(`SELECT id, url, writer_id, account_id, trello_card_id
             FROM video
             WHERE trello_card_id = ANY($1) AND url IS NOT NULL AND url != ''`, [
                        trelloCardIds
                    ]);
                    console.log(`YouTube URLs from video table for trello_card_ids:`, trelloVideoResult.rows);
                    // Add these videos to our videoResult if they're not already there
                    for (const video of trelloVideoResult.rows){
                        if (!videoResult.rows.some((v)=>v.id === video.id)) {
                            videoResult.rows.push(video);
                        }
                    }
                }
                // If we found YouTube URLs in the video table, update the resources
                if (videoResult.rows.length > 0) {
                    // For each resource, check if there's a matching YouTube URL in the video table
                    for (const resource of variable.resources){
                        // Try to find a matching video by trello_card_id if it exists
                        const matchingVideo = videoResult.rows.find((video)=>resource.trello_card_id && video.trello_card_id && resource.trello_card_id === video.trello_card_id);
                        if (matchingVideo) {
                            // Update the resource with the YouTube URL from the video table
                            resource.youtube_url = matchingVideo.url;
                            resource.video_id = matchingVideo.id;
                            resource.from_video_table = true;
                            console.log(`Updated resource ${resource.id} with YouTube URL from video table:`, matchingVideo.url);
                        } else if (resource.youtube_url_from_note) {
                            // Check if there's a video with a URL that matches the one from the note
                            const videoIdFromNote = extractYoutubeVideoId(resource.youtube_url_from_note);
                            if (videoIdFromNote) {
                                const matchingVideoFromNote = videoResult.rows.find((video)=>{
                                    const videoId = extractYoutubeVideoId(video.url);
                                    return videoId === videoIdFromNote;
                                });
                                if (matchingVideoFromNote) {
                                    resource.youtube_url = matchingVideoFromNote.url;
                                    resource.video_id = matchingVideoFromNote.id;
                                    resource.from_video_table = true;
                                    console.log(`Updated resource ${resource.id} with YouTube URL from note match:`, matchingVideoFromNote.url);
                                } else {
                                    // If no match in video table, use the URL from the note
                                    resource.youtube_url = resource.youtube_url_from_note;
                                    console.log(`Updated resource ${resource.id} with YouTube URL from note:`, resource.youtube_url_from_note);
                                }
                            }
                        }
                    }
                    // If there are more videos than resources, add them as new resources
                    for (const video of videoResult.rows){
                        // Check if this video is already associated with a resource
                        const existingResource = variable.resources.find((resource)=>resource.trello_card_id && video.trello_card_id && resource.trello_card_id === video.trello_card_id || resource.youtube_url === video.url || resource.youtube_url_from_note && extractYoutubeVideoId(resource.youtube_url_from_note) === extractYoutubeVideoId(video.url));
                        if (!existingResource) {
                            // Add a new resource with this YouTube URL
                            variable.resources.push({
                                id: null,
                                google_doc_link: '',
                                youtube_url: video.url,
                                note: `YouTube video from Trello (ID: ${video.id})`,
                                account: '',
                                created_at: new Date().toISOString(),
                                video_id: video.id,
                                from_video_table: true // Flag to indicate this came from the video table
                            });
                            console.log(`Added new resource with YouTube URL from video table:`, video.url);
                        }
                    }
                } else {
                    // If no videos found in the video table, check if any resources have YouTube URLs in their notes
                    for (const resource of variable.resources){
                        if (resource.youtube_url_from_note && !resource.youtube_url) {
                            resource.youtube_url = resource.youtube_url_from_note;
                            console.log(`Using YouTube URL from note for resource ${resource.id}:`, resource.youtube_url_from_note);
                        }
                    }
                }
                // Process YouTube URLs for metrics
                for (const resource of variable.resources){
                    // Debug: Log each resource's YouTube URL
                    console.log(`Resource ${resource.id} final YouTube URL:`, resource.youtube_url);
                    if (resource.youtube_url) {
                        // Add a flag to indicate this is a YouTube resource that needs metrics
                        resource.needs_metrics = true;
                        // Extract YouTube video ID from URL (for future reference)
                        try {
                            const url = new URL(resource.youtube_url);
                            const videoId = url.searchParams.get('v');
                            if (videoId) {
                                resource.youtube_id = videoId;
                            }
                        } catch (error) {
                            console.error('Error parsing YouTube URL:', error);
                        }
                    }
                }
            }
            // Add variables to the project
            project.variables = variables;
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                project
            });
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error fetching project:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch project'
        }, {
            status: 500
        });
    }
}
async function PUT(request, { params }) {
    try {
        const { id } = await params;
        const projectId = id;
        const projectData = await request.json();
        // Validate input
        if (!projectData.name) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Project name is required'
            }, {
                status: 400
            });
        }
        // Connect to PostgreSQL
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].connect();
        try {
            // Start a transaction
            await client.query('BEGIN');
            // Check if project exists
            const projectCheck = await client.query('SELECT id FROM src_projects WHERE id = $1', [
                projectId
            ]);
            if (projectCheck.rows.length === 0) {
                await client.query('ROLLBACK');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Project not found'
                }, {
                    status: 404
                });
            }
            // Update project
            await client.query(`UPDATE src_projects
         SET name = $1, description = $2, members = $3, created_by = $4
         WHERE id = $5`, [
                projectData.name,
                projectData.description || '',
                projectData.members || 1,
                projectData.assignedTo || 'clay',
                projectId
            ]);
            // Handle variables
            if (projectData.variables && Array.isArray(projectData.variables)) {
                for (const variable of projectData.variables){
                    if (variable.id) {
                        // Update existing variable
                        await client.query(`UPDATE src_project_variables
               SET name = $1, note = $2
               WHERE id = $3 AND project_id = $4`, [
                            variable.name,
                            variable.note || '',
                            variable.id,
                            projectId
                        ]);
                    } else if (variable.name) {
                        // Insert new variable
                        const variableResult = await client.query(`INSERT INTO src_project_variables (project_id, name, note)
               VALUES ($1, $2, $3)
               RETURNING id`, [
                            projectId,
                            variable.name,
                            variable.note || ''
                        ]);
                        variable.id = variableResult.rows[0].id;
                    }
                    // Handle resources for this variable
                    if (variable.id && variable.resources && Array.isArray(variable.resources)) {
                        for (const resource of variable.resources){
                            // Check if this is a resource from the video table
                            if (resource.from_video_table && resource.video_id) {
                                console.log(`Handling resource from video table: video_id=${resource.video_id}, YouTube URL=${resource.youtubeUrl}`);
                                // Update the video table directly
                                if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {
                                    await client.query(`UPDATE video
                     SET url = $1
                     WHERE id = $2`, [
                                        resource.youtubeUrl,
                                        resource.video_id
                                    ]);
                                    console.log(`Updated video table: ID=${resource.video_id}, YouTube URL=${resource.youtubeUrl}`);
                                }
                                continue;
                            }
                            if (resource.id) {
                                // Get the current resource data to check if YouTube URL has changed
                                const currentResourceResult = await client.query(`SELECT youtube_url FROM src_variable_resources WHERE id = $1 AND variable_id = $2`, [
                                    resource.id,
                                    variable.id
                                ]);
                                const oldYoutubeUrl = currentResourceResult.rows.length > 0 ? currentResourceResult.rows[0].youtube_url : '';
                                const newYoutubeUrl = resource.youtubeUrl || '';
                                // Update existing resource
                                await client.query(`UPDATE src_variable_resources
                   SET google_doc_link = $1, youtube_url = $2, note = $3, account = $4
                   WHERE id = $5 AND variable_id = $6`, [
                                    resource.googleDocLink || '',
                                    newYoutubeUrl,
                                    resource.note || '',
                                    resource.account || '',
                                    resource.id,
                                    variable.id
                                ]);
                                // Debug log
                                console.log(`Updated resource in src_variable_resources: ID=${resource.id}, YouTube URL=${newYoutubeUrl}`);
                                // If YouTube URL was changed, update the video table
                                if (oldYoutubeUrl !== newYoutubeUrl) {
                                    // Extract video IDs for comparison
                                    const oldVideoId = extractYoutubeVideoId(oldYoutubeUrl);
                                    const newVideoId = extractYoutubeVideoId(newYoutubeUrl);
                                    // If the video IDs are the same despite different URLs, no need to update
                                    if (oldVideoId && newVideoId && oldVideoId === newVideoId) {
                                        // URLs are different but point to the same video, just update the URL
                                        await client.query(`UPDATE video
                       SET url = $1
                       WHERE url = $2`, [
                                            newYoutubeUrl,
                                            oldYoutubeUrl
                                        ]);
                                    } else {
                                        // Different videos, proceed with the update
                                        // If old URL exists and is not empty, clear project_id and variable_id
                                        if (oldYoutubeUrl && oldYoutubeUrl.trim() !== '') {
                                            // Get existing writer_id and account_id to preserve them
                                            const oldVideoResult = await client.query('SELECT writer_id, account_id FROM video WHERE url LIKE $1', [
                                                `%${oldVideoId}%`
                                            ]);
                                            if (oldVideoResult.rows.length > 0) {
                                                // Clear project_id and variable_id but preserve writer_id and account_id
                                                await client.query(`UPDATE video
                           SET project_id = NULL, variable_id = NULL
                           WHERE url LIKE $1`, [
                                                    `%${oldVideoId}%`
                                                ]);
                                            }
                                        }
                                        // If new URL exists and is not empty, set project_id and variable_id
                                        if (newYoutubeUrl && newYoutubeUrl.trim() !== '' && newVideoId) {
                                            // Check if this video ID already exists in the video table
                                            const videoCheck = await client.query('SELECT id, writer_id, account_id, url FROM video WHERE url LIKE $1', [
                                                `%${newVideoId}%`
                                            ]);
                                            if (videoCheck.rows.length === 0) {
                                                // Get the account_id from the posting_accounts table
                                                let accountId1 = null;
                                                try {
                                                    const accountResult = await client.query(`SELECT id FROM posting_accounts WHERE account = $1`, [
                                                        resource.account || 'clay'
                                                    ]);
                                                    if (accountResult.rows.length > 0) {
                                                        accountId1 = accountResult.rows[0].id;
                                                    }
                                                } catch (error) {
                                                    console.error('Error getting account ID:', error);
                                                }
                                                // Insert into video table with project_id and variable_id
                                                await client.query(`INSERT INTO video (url, account_id, created, writer_id, project_id, variable_id)
                           VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`, [
                                                    newYoutubeUrl,
                                                    accountId1 || 1,
                                                    129,
                                                    projectId,
                                                    variable.id
                                                ]);
                                            } else {
                                                // Video exists with a different URL format, update with project_id and variable_id
                                                // while preserving writer_id and account_id
                                                await client.query(`UPDATE video
                           SET project_id = $1, variable_id = $2
                           WHERE id = $3`, [
                                                    projectId,
                                                    variable.id,
                                                    videoCheck.rows[0].id
                                                ]);
                                            }
                                        }
                                    }
                                } else if (newYoutubeUrl && newYoutubeUrl.trim() !== '') {
                                    // URL didn't change but make sure project_id and variable_id are set
                                    const videoId = extractYoutubeVideoId(newYoutubeUrl);
                                    if (videoId) {
                                        await client.query(`UPDATE video
                       SET project_id = $1, variable_id = $2
                       WHERE url LIKE $3`, [
                                            projectId,
                                            variable.id,
                                            `%${videoId}%`
                                        ]);
                                    }
                                }
                            } else if (resource.googleDocLink || resource.youtubeUrl) {
                                // Insert new resource
                                await client.query(`INSERT INTO src_variable_resources (variable_id, google_doc_link, youtube_url, note, account)
                   VALUES ($1, $2, $3, $4, $5)`, [
                                    variable.id,
                                    resource.googleDocLink || '',
                                    resource.youtubeUrl || '',
                                    resource.note || '',
                                    resource.account || ''
                                ]);
                                // If it's a YouTube URL, also insert into the video table for the metadata scraper
                                if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {
                                    // Extract the video ID
                                    const videoId = extractYoutubeVideoId(resource.youtubeUrl);
                                    if (videoId) {
                                        // Check if this video ID already exists in the video table
                                        const videoCheck = await client.query('SELECT id, writer_id, account_id FROM video WHERE url LIKE $1', [
                                            `%${videoId}%`
                                        ]);
                                        // Get the account_id from the posting_accounts table
                                        let accountId1 = null;
                                        try {
                                            const accountResult = await client.query(`SELECT id FROM posting_accounts WHERE account = $1`, [
                                                resource.account || 'clay'
                                            ]);
                                            if (accountResult.rows.length > 0) {
                                                accountId1 = accountResult.rows[0].id;
                                            }
                                        } catch (error) {
                                            console.error('Error getting account ID:', error);
                                        }
                                        if (videoCheck.rows.length === 0) {
                                            // Video doesn't exist, insert it into the video table with the actual variable_id
                                            await client.query(`INSERT INTO video (url, account_id, created, project_id, variable_id, writer_id)
                         VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`, [
                                                resource.youtubeUrl,
                                                accountId1 || 1,
                                                projectId,
                                                variable.id,
                                                129 // Always set writer_id to 129
                                            ]);
                                        } else {
                                            // Video exists with a different URL format, check if it has a writer_id and account_id
                                            const existingWriterId = videoCheck.rows[0].writer_id;
                                            const existingAccountId = videoCheck.rows[0].account_id;
                                            if (existingWriterId && existingAccountId) {
                                                // If both writer_id and account_id exist, just update project_id and variable_id
                                                await client.query(`UPDATE video
                           SET project_id = $1, variable_id = $2
                           WHERE id = $3`, [
                                                    projectId,
                                                    variable.id,
                                                    videoCheck.rows[0].id
                                                ]);
                                            } else if (existingWriterId && !existingAccountId) {
                                                // If writer_id exists but no account_id, update with new account_id
                                                await client.query(`UPDATE video
                           SET project_id = $1, variable_id = $2, account_id = $3
                           WHERE id = $4`, [
                                                    projectId,
                                                    variable.id,
                                                    accountId1 || 1,
                                                    videoCheck.rows[0].id
                                                ]);
                                            } else if (!existingWriterId && existingAccountId) {
                                                // If account_id exists but no writer_id, update with writer_id = 129
                                                await client.query(`UPDATE video
                           SET project_id = $1, variable_id = $2, writer_id = $3
                           WHERE id = $4`, [
                                                    projectId,
                                                    variable.id,
                                                    129,
                                                    videoCheck.rows[0].id
                                                ]);
                                            } else {
                                                // If neither exists, update both
                                                await client.query(`UPDATE video
                           SET project_id = $1, variable_id = $2, writer_id = $3, account_id = $4
                           WHERE id = $5`, [
                                                    projectId,
                                                    variable.id,
                                                    129,
                                                    accountId1 || 1,
                                                    videoCheck.rows[0].id
                                                ]);
                                            }
                                        }
                                    }
                                }
                                // Only create a Trello card if there's a Google Doc link AND no YouTube link
                                if (resource.googleDocLink && resource.googleDocLink.trim() !== '' && (!resource.youtubeUrl || resource.youtubeUrl.trim() === '')) {
                                    // Import axios for making HTTP requests from the server
                                    const axios = __turbopack_context__.r("[project]/node_modules/axios/dist/node/axios.cjs [app-route] (ecmascript)");
                                    try {
                                        // For new resources, resource.id will be undefined, so we need a different approach
                                        let resourceIndex;
                                        if (resource.id) {
                                            // For existing resources, find the index
                                            resourceIndex = variable.resources.findIndex((r)=>r.id === resource.id) + 1;
                                        } else {
                                            // For new resources, count how many resources this variable has
                                            const resourceCountResult = await client.query(`SELECT COUNT(*) FROM src_variable_resources WHERE variable_id = $1`, [
                                                variable.id
                                            ]);
                                            resourceIndex = parseInt(resourceCountResult.rows[0].count) + 1;
                                        }
                                        // Create a title in the format: {project name}: {variable name}:{resource number}
                                        const cardTitle = `${projectData.name}: ${variable.name}:${resourceIndex}`;
                                        // Call the Trello API endpoint directly
                                        const trelloData = {
                                            title: cardTitle,
                                            googleDocLink: resource.googleDocLink,
                                            posting_account: resource.account || 'clay',
                                            variable_id: variable.id,
                                            project_id: projectId
                                        };
                                        // Fetch Trello settings
                                        const settingsResult = await client.query("SELECT api_key, token FROM settings ORDER BY id DESC LIMIT 1");
                                        if (settingsResult.rows.length === 0) {
                                            console.error('Trello settings not configured');
                                            return;
                                        }
                                        const { api_key: apiKey, token: apiToken } = settingsResult.rows[0];
                                        // Create the Trello card directly
                                        const autoApprovedListID = "66982de89e8cb1bfb456ba0a";
                                        // Create the card
                                        const cardResponse = await axios.post('https://api.trello.com/1/cards', null, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken,
                                                idList: autoApprovedListID,
                                                name: cardTitle,
                                                desc: `Google Doc Link: ${resource.googleDocLink}`,
                                                pos: 'top'
                                            }
                                        });
                                        const trelloCardId = cardResponse.data.id;
                                        // Add attachment
                                        await axios.post(`https://api.trello.com/1/cards/${trelloCardId}/attachments`, null, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken,
                                                url: resource.googleDocLink
                                            }
                                        });
                                        // Discover board and custom-field IDs (for Posting Account)
                                        const cardDetails = await axios.get(`https://api.trello.com/1/cards/${trelloCardId}`, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken
                                            }
                                        });
                                        const boardId = cardDetails.data.idBoard;
                                        const customFields = await axios.get(`https://api.trello.com/1/boards/${boardId}/customFields`, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken
                                            }
                                        });
                                        const postingAccountField = customFields.data.find((f)=>f.name === "Posting Account");
                                        if (postingAccountField) {
                                            // Set Posting Account custom field
                                            await axios.put(`https://api.trello.com/1/cards/${trelloCardId}/customField/${postingAccountField.id}/item`, {
                                                value: {
                                                    text: resource.account || 'clay'
                                                }
                                            }, {
                                                params: {
                                                    key: apiKey,
                                                    token: apiToken
                                                }
                                            });
                                        }
                                        // Insert into video table with project_id and variable_id
                                        await client.query(`INSERT INTO video (trello_card_id, variable_id, project_id)
                       VALUES ($1, $2, $3)`, [
                                            trelloCardId,
                                            variable.id,
                                            projectId
                                        ]);
                                        // Insert script into DB with writer_id=129
                                        const insertRes = await client.query(`INSERT INTO script (
                         title,
                         google_doc_link,
                         approval_status,
                         created_at,
                         writer_id,
                         account_id
                       )
                       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5)
                       RETURNING *`, [
                                            cardTitle,
                                            resource.googleDocLink,
                                            "Approved Script. Ready for production",
                                            129,
                                            accountId || 1 // Use the account_id we got earlier or default to 1
                                        ]);
                                        const script = insertRes.rows[0];
                                        // Update script with Trello card ID
                                        await client.query("UPDATE script SET trello_card_id = $1 WHERE id = $2", [
                                            trelloCardId,
                                            script.id
                                        ]);
                                    } catch (trelloError) {
                                        console.error('Error creating Trello card:', trelloError);
                                    // Continue even if Trello card creation fails
                                    }
                                }
                            }
                        }
                        // Handle deleted resources (if deletedResourceIds is provided)
                        if (variable.deletedResourceIds && Array.isArray(variable.deletedResourceIds)) {
                            for (const resourceId of variable.deletedResourceIds){
                                await client.query('DELETE FROM src_variable_resources WHERE id = $1 AND variable_id = $2', [
                                    resourceId,
                                    variable.id
                                ]);
                            }
                        }
                    }
                }
                // Handle deleted variables (if deletedVariableIds is provided)
                if (projectData.deletedVariableIds && Array.isArray(projectData.deletedVariableIds)) {
                    for (const variableId of projectData.deletedVariableIds){
                        // Delete associated resources first
                        await client.query('DELETE FROM src_variable_resources WHERE variable_id = $1', [
                            variableId
                        ]);
                        // Then delete the variable
                        await client.query('DELETE FROM src_project_variables WHERE id = $1 AND project_id = $2', [
                            variableId,
                            projectId
                        ]);
                    }
                }
            }
            // Commit the transaction
            await client.query('COMMIT');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Project updated successfully'
            });
        } catch (dbError) {
            // Rollback in case of error
            await client.query('ROLLBACK');
            console.error('Database error:', dbError);
            throw dbError;
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error updating project:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to update project'
        }, {
            status: 500
        });
    }
}
async function DELETE(request, { params }) {
    try {
        const { id } = await params;
        const projectId = id;
        // Connect to PostgreSQL
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$db$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].connect();
        try {
            // Start a transaction
            await client.query('BEGIN');
            // Check if project exists
            const projectCheck = await client.query('SELECT id FROM src_projects WHERE id = $1', [
                projectId
            ]);
            if (projectCheck.rows.length === 0) {
                await client.query('ROLLBACK');
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Project not found'
                }, {
                    status: 404
                });
            }
            // Get all variables for this project
            const variablesResult = await client.query('SELECT id FROM src_project_variables WHERE project_id = $1', [
                projectId
            ]);
            // Delete resources for each variable
            for (const variable of variablesResult.rows){
                await client.query('DELETE FROM src_variable_resources WHERE variable_id = $1', [
                    variable.id
                ]);
            }
            // Delete variables
            await client.query('DELETE FROM src_project_variables WHERE project_id = $1', [
                projectId
            ]);
            // Delete project
            await client.query('DELETE FROM src_projects WHERE id = $1', [
                projectId
            ]);
            // Commit the transaction
            await client.query('COMMIT');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Project deleted successfully'
            });
        } catch (dbError) {
            // Rollback in case of error
            await client.query('ROLLBACK');
            console.error('Database error:', dbError);
            throw dbError;
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error deleting project:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to delete project'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__01adfcae._.js.map