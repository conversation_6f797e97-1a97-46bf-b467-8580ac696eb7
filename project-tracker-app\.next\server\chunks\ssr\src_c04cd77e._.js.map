{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/components/RechartsLineChart.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  <PERSON><PERSON>hart,\n  Line,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  ResponsiveContainer,\n  TooltipProps\n} from 'recharts';\nimport { useState } from 'react';\n\ninterface DataPoint {\n  date: string;\n  value: number;\n}\n\ninterface RechartsLineChartProps {\n  data: DataPoint[];\n  label: string;\n  color?: string;\n  height?: number;\n}\n\n// Custom tooltip component\nconst CustomTooltip = ({ active, payload, label }: TooltipProps<number, string>) => {\n  if (active && payload && payload.length) {\n    return (\n      <div className=\"bg-gray-800 text-white p-2 rounded-md shadow-lg text-xs\">\n        <p className=\"font-medium\">{label}</p>\n        <p className=\"font-bold\">{payload[0].value?.toLocaleString()}</p>\n      </div>\n    );\n  }\n  return null;\n};\n\nexport default function RechartsLineChart({ \n  data, \n  label, \n  color = '#4f46e5',\n  height = 150\n}: RechartsLineChartProps) {\n  // If no data, show a message\n  if (!data || data.length === 0) {\n    return (\n      <div className=\"text-gray-400 text-xs p-2 h-[150px] flex items-center justify-center border border-gray-200 rounded\">\n        No data available yet\n      </div>\n    );\n  }\n\n  // Format data for Recharts\n  const chartData = data.map(item => ({\n    date: item.date,\n    value: item.value\n  }));\n\n  return (\n    <div className=\"w-full\">\n      <div className=\"text-sm font-medium text-gray-700 mb-1\">{label}</div>\n      <div style={{ width: '100%', height: `${height}px` }}>\n        <ResponsiveContainer width=\"100%\" height=\"100%\">\n          <LineChart\n            data={chartData}\n            margin={{ top: 5, right: 5, left: 0, bottom: 5 }}\n          >\n            <defs>\n              <linearGradient id={`color-${label}`} x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\n                <stop offset=\"5%\" stopColor={color} stopOpacity={0.2} />\n                <stop offset=\"95%\" stopColor={color} stopOpacity={0} />\n              </linearGradient>\n            </defs>\n            <CartesianGrid strokeDasharray=\"3 3\" vertical={false} stroke=\"#f0f0f0\" />\n            <XAxis \n              dataKey=\"date\" \n              tick={{ fontSize: 10 }} \n              tickLine={false}\n              axisLine={{ stroke: '#e5e7eb' }}\n            />\n            <YAxis \n              tick={{ fontSize: 10 }} \n              tickFormatter={(value) => value.toLocaleString()}\n              tickLine={false}\n              axisLine={false}\n              width={35}\n            />\n            <Tooltip \n              content={<CustomTooltip />}\n              cursor={{ stroke: color, strokeWidth: 1, strokeDasharray: '3 3' }}\n            />\n            <Line\n              type=\"monotone\"\n              dataKey=\"value\"\n              stroke={color}\n              strokeWidth={2}\n              dot={data.length < 10 ? { stroke: color, strokeWidth: 2, r: 3 } : false}\n              activeDot={{ stroke: 'white', strokeWidth: 2, r: 5, fill: color }}\n              name={label}\n              fill={`url(#color-${label})`}\n            />\n          </LineChart>\n        </ResponsiveContainer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AA0BA,2BAA2B;AAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAgC;IAC7E,IAAI,UAAU,WAAW,QAAQ,MAAM,EAAE;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAAe;;;;;;8BAC5B,8OAAC;oBAAE,WAAU;8BAAa,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE;;;;;;;;;;;;IAGlD;IACA,OAAO;AACT;AAEe,SAAS,kBAAkB,EACxC,IAAI,EACJ,KAAK,EACL,QAAQ,SAAS,EACjB,SAAS,GAAG,EACW;IACvB,6BAA6B;IAC7B,IAAI,CAAC,QAAQ,KAAK,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC;YAAI,WAAU;sBAAsG;;;;;;IAIzH;IAEA,2BAA2B;IAC3B,MAAM,YAAY,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;YAClC,MAAM,KAAK,IAAI;YACf,OAAO,KAAK,KAAK;QACnB,CAAC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAA0C;;;;;;0BACzD,8OAAC;gBAAI,OAAO;oBAAE,OAAO;oBAAQ,QAAQ,GAAG,OAAO,EAAE,CAAC;gBAAC;0BACjD,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;oBAAC,OAAM;oBAAO,QAAO;8BACvC,cAAA,8OAAC,qJAAA,CAAA,YAAS;wBACR,MAAM;wBACN,QAAQ;4BAAE,KAAK;4BAAG,OAAO;4BAAG,MAAM;4BAAG,QAAQ;wBAAE;;0CAE/C,8OAAC;0CACC,cAAA,8OAAC;oCAAe,IAAI,CAAC,MAAM,EAAE,OAAO;oCAAE,IAAG;oCAAI,IAAG;oCAAI,IAAG;oCAAI,IAAG;;sDAC5D,8OAAC;4CAAK,QAAO;4CAAK,WAAW;4CAAO,aAAa;;;;;;sDACjD,8OAAC;4CAAK,QAAO;4CAAM,WAAW;4CAAO,aAAa;;;;;;;;;;;;;;;;;0CAGtD,8OAAC,6JAAA,CAAA,gBAAa;gCAAC,iBAAgB;gCAAM,UAAU;gCAAO,QAAO;;;;;;0CAC7D,8OAAC,qJAAA,CAAA,QAAK;gCACJ,SAAQ;gCACR,MAAM;oCAAE,UAAU;gCAAG;gCACrB,UAAU;gCACV,UAAU;oCAAE,QAAQ;gCAAU;;;;;;0CAEhC,8OAAC,qJAAA,CAAA,QAAK;gCACJ,MAAM;oCAAE,UAAU;gCAAG;gCACrB,eAAe,CAAC,QAAU,MAAM,cAAc;gCAC9C,UAAU;gCACV,UAAU;gCACV,OAAO;;;;;;0CAET,8OAAC,uJAAA,CAAA,UAAO;gCACN,uBAAS,8OAAC;;;;;gCACV,QAAQ;oCAAE,QAAQ;oCAAO,aAAa;oCAAG,iBAAiB;gCAAM;;;;;;0CAElE,8OAAC,oJAAA,CAAA,OAAI;gCACH,MAAK;gCACL,SAAQ;gCACR,QAAQ;gCACR,aAAa;gCACb,KAAK,KAAK,MAAM,GAAG,KAAK;oCAAE,QAAQ;oCAAO,aAAa;oCAAG,GAAG;gCAAE,IAAI;gCAClE,WAAW;oCAAE,QAAQ;oCAAS,aAAa;oCAAG,GAAG;oCAAG,MAAM;gCAAM;gCAChE,MAAM;gCACN,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1C", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/dashboard/projects/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { usePara<PERSON>, useRouter } from 'next/navigation';\nimport {\n  FaChartLine,\n  FaYoutube,\n  FaEye,\n  FaThumbsUp,\n  FaArrowLeft,\n  FaLink\n} from 'react-icons/fa';\nimport RechartsLineChart from '@/components/RechartsLineChart';\nimport { format } from 'date-fns';\n\n// Helper function to format time series data for charts\nconst formatTimeSeriesData = (data, field = 'views') => {\n  if (!data || !data.length) {\n    // Return empty array if no data\n    return [];\n  }\n\n  try {\n    // Check if all data points are from the same day\n    const dates = data.map(item => new Date(item.date || new Date()));\n    const allSameDay = dates.length > 0 && dates.every(date =>\n      date.getDate() === dates[0].getDate() &&\n      date.getMonth() === dates[0].getMonth() &&\n      date.getFullYear() === dates[0].getFullYear()\n    );\n\n    // If all data is from the same day and we have few points, show hours instead of days\n    const formatString = (allSameDay && dates.length < 5) ? 'h:mm a' : 'MMM dd';\n\n    return data.map(item => ({\n      date: format(new Date(item.date || new Date()), formatString),\n      value: Math.round(item[field] || 0)\n    }));\n  } catch (error) {\n    console.error(`Error formatting time series data for ${field}:`, error);\n    return [];\n  }\n};\n\n// Helper function to calculate median\nconst calculateMedian = (values) => {\n  if (!values || !values.length) return 0;\n\n  const sorted = [...values].sort((a, b) => a - b);\n  const middle = Math.floor(sorted.length / 2);\n\n  if (sorted.length % 2 === 0) {\n    return Math.round((sorted[middle - 1] + sorted[middle]) / 2);\n  }\n\n  return Math.round(sorted[middle]);\n};\n\n// Function to fetch real video metrics from InfluxDB\nconst fetchVideoMetrics = async (videoUrl) => {\n  if (!videoUrl || videoUrl.trim() === '') {\n    return [];\n  }\n\n  try {\n    const encodedUrl = encodeURIComponent(videoUrl);\n    const response = await fetch(`/api/metrics?type=video&videoUrl=${encodedUrl}`);\n    const data = await response.json();\n    return data.metrics || [];\n  } catch (error) {\n    console.error(`Error fetching metrics for video ${videoUrl}:`, error);\n    return [];\n  }\n};\n\n// Function to fetch video thumbnail from InfluxDB\nconst fetchVideoThumbnail = async (videoUrl) => {\n  if (!videoUrl || videoUrl.trim() === '') {\n    return '';\n  }\n\n  try {\n    const encodedUrl = encodeURIComponent(videoUrl);\n    const response = await fetch(`/api/metrics?type=thumbnail&videoUrl=${encodedUrl}`);\n    const data = await response.json();\n    return data.thumbnailUrl || '';\n  } catch (error) {\n    console.error(`Error fetching thumbnail for video ${videoUrl}:`, error);\n    return '';\n  }\n};\n\n// Function to fetch project metrics from our new API endpoint\nconst fetchProjectMetrics = async (projectId, variableId = null) => {\n  try {\n    let url = `/api/metrics/project?projectId=${projectId}`;\n    if (variableId) {\n      url += `&variableId=${variableId}`;\n    }\n\n    const response = await fetch(url);\n    const data = await response.json();\n\n    // Check if this is a single video (added in the updated getAggregateMetrics function)\n    const isSingleVideo = data.aggregateMetrics?.isSingleVideo || false;\n\n    return {\n      avgViews: Math.round(data.aggregateMetrics?.views?.avg || 0),\n      avgLikes: Math.round(data.aggregateMetrics?.likes?.avg || 0),\n      avgComments: Math.round(data.aggregateMetrics?.comments?.avg || 0),\n      medianViews: Math.round(data.aggregateMetrics?.views?.median || 0),\n      medianLikes: Math.round(data.aggregateMetrics?.likes?.median || 0),\n      medianComments: Math.round(data.aggregateMetrics?.comments?.median || 0),\n      viewsOverTime: formatTimeSeriesData(data.timeSeries || [], 'views'),\n      likesOverTime: formatTimeSeriesData(data.timeSeries || [], 'likes'),\n      commentsOverTime: formatTimeSeriesData(data.timeSeries || [], 'comments'),\n      metrics: data.metrics || [],\n      isSingleVideo: isSingleVideo\n    };\n  } catch (error) {\n    console.error(`Error fetching metrics for project/variable:`, error);\n    return {\n      avgViews: 0,\n      avgLikes: 0,\n      avgComments: 0,\n      medianViews: 0,\n      medianLikes: 0,\n      medianComments: 0,\n      viewsOverTime: [],\n      likesOverTime: [],\n      commentsOverTime: [],\n      metrics: [],\n      isSingleVideo: false\n    };\n  }\n};\n\n// Function to fetch real variable metrics from InfluxDB\nconst fetchVariableMetrics = async (videoUrls) => {\n  if (!videoUrls || !videoUrls.length) {\n    return {\n      avgViews: 0,\n      avgLikes: 0,\n      avgComments: 0,\n      medianViews: 0,\n      medianLikes: 0,\n      medianComments: 0,\n      viewsOverTime: [],\n      likesOverTime: [],\n      commentsOverTime: [],\n      isSingleVideo: false\n    };\n  }\n\n  try {\n    // Check if this is a single video\n    const isSingleVideo = videoUrls.length === 1;\n\n    // Encode all URLs\n    const encodedUrls = videoUrls.map(url => encodeURIComponent(url));\n\n    // Fetch aggregate metrics\n    const aggregateResponse = await fetch(`/api/metrics?type=aggregate&videoUrls=${encodedUrls.join(',')}`);\n    const aggregateData = await aggregateResponse.json();\n\n    // Fetch time series data\n    const timeSeriesResponse = await fetch(`/api/metrics?type=timeseries&videoUrls=${encodedUrls.join(',')}`);\n    const timeSeriesData = await timeSeriesResponse.json();\n\n    // Get variable metrics for median calculation\n    const variableResponse = await fetch(`/api/metrics?type=variable&videoUrls=${encodedUrls.join(',')}`);\n    const variableData = await variableResponse.json();\n\n    // Use the median values from the aggregate metrics\n    return {\n      avgViews: Math.round(aggregateData.metrics?.views?.avg || 0),\n      avgLikes: Math.round(aggregateData.metrics?.likes?.avg || 0),\n      avgComments: Math.round(aggregateData.metrics?.comments?.avg || 0),\n      medianViews: Math.round(aggregateData.metrics?.views?.median || 0),\n      medianLikes: Math.round(aggregateData.metrics?.likes?.median || 0),\n      medianComments: Math.round(aggregateData.metrics?.comments?.median || 0),\n      viewsOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'views'),\n      likesOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'likes'),\n      commentsOverTime: formatTimeSeriesData(timeSeriesData.timeSeries || [], 'comments'),\n      isSingleVideo: isSingleVideo || aggregateData.metrics?.isSingleVideo || false\n    };\n  } catch (error) {\n    console.error(`Error fetching metrics for variable:`, error);\n    return {\n      avgViews: 0,\n      avgLikes: 0,\n      avgComments: 0,\n      medianViews: 0,\n      medianLikes: 0,\n      medianComments: 0,\n      viewsOverTime: [],\n      likesOverTime: [],\n      commentsOverTime: [],\n      isSingleVideo: videoUrls.length === 1\n    };\n  }\n};\n\n// Function to fetch YouTube videos for a variable\nconst fetchVariableVideos = async (variableId) => {\n  try {\n    const response = await fetch(`/api/videos?variableId=${variableId}`);\n    if (!response.ok) throw new Error('Failed to fetch videos');\n\n    const data = await response.json();\n\n    // Map the response to the format expected by the rest of the code\n    return data.videos.map(video => ({\n      id: video.id,\n      url: video.url,\n      title: video.title || `YouTube Video ${video.id}`,\n      created: video.created,\n      writer_id: video.writer_id,\n      account_id: video.account_id,\n      video_cat: video.video_cat\n    }));\n  } catch (error) {\n    console.error(`Error fetching videos for variable ${variableId}:`, error);\n    return [];\n  }\n};\n\n// Fallback mock data generator for when real data is unavailable\nconst generateMockTimeData = (days = 30, baseValue = 1000, volatility = 0.2) => {\n  const data = [];\n  let currentValue = baseValue;\n\n  for (let i = 0; i < days; i++) {\n    // Generate a date days ago from today\n    const date = new Date();\n    date.setDate(date.getDate() - (days - i));\n\n    // Random walk with volatility\n    const change = currentValue * (Math.random() * volatility * 2 - volatility);\n    currentValue = Math.max(0, currentValue + change);\n\n    data.push({\n      date: format(date, 'MMM dd'),\n      value: Math.round(currentValue)\n    });\n  }\n\n  return data;\n};\n\n// We're now using the RechartsLineChart component instead of a custom LineChart\nconst LineChart = RechartsLineChart;\n\nexport default function ProjectDetail() {\n  const router = useRouter();\n  const params = useParams();\n  const [project, setProject] = useState<any>(null);\n  const [variables, setVariables] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [selectedVariable, setSelectedVariable] = useState<any>(null);\n\n  useEffect(() => {\n    // Load project from the database\n    const loadProject = async () => {\n      try {\n        // Get the ID from params\n        const id = params?.id;\n        if (!id) return;\n\n        // Try to fetch from the API\n        const response = await fetch(`/api/projects/${id}`);\n\n        if (response.ok) {\n          const data = await response.json();\n          const project = data.project;\n\n          if (project) {\n            // Format the project data\n            const formattedProject = {\n              id: project.id,\n              name: project.name,\n              description: project.description,\n              dueDate: new Date(project.created_at).toISOString().split('T')[0],\n              createdBy: project.created_by,\n              members: project.members || 1\n            };\n\n            setProject(formattedProject);\n\n            // Process variables and their resources\n            if (project.variables && project.variables.length > 0) {\n              // Process each variable\n              const processVariablesWithMetrics = async () => {\n                const processedVariables = [];\n\n                for (const variable of project.variables) {\n                  // Get YouTube videos for this variable from the video table\n                  // We'll fetch videos directly from the video table using the variable ID\n                  const youtubeResources = await fetchVariableVideos(variable.id);\n\n                  // If we have YouTube resources, fetch metrics from InfluxDB\n                  let variableMetrics = null;\n                  let videos = [];\n\n                  if (youtubeResources.length > 0) {\n                    // Get video URLs for InfluxDB queries\n                    const videoUrls = youtubeResources.map(r => r.url);\n\n                    // Fetch metrics for this variable using the project_id and variable_id\n                    variableMetrics = await fetchProjectMetrics(project.id, variable.id);\n\n                    // Process individual videos with their metrics\n                    for (const resource of youtubeResources) {\n                      try {\n                        // Fetch metrics for this specific video\n                        const videoMetrics = await fetchVideoMetrics(resource.url);\n\n                        // Fetch thumbnail for this video\n                        const thumbnailUrl = await fetchVideoThumbnail(resource.url);\n\n                        // Calculate views, likes, and comments from metrics\n                        let views = 0;\n                        let likes = 0;\n                        let comments = 0;\n                        let viewsData = [];\n                        let likesData = [];\n                        let commentsData = [];\n\n                        if (videoMetrics && videoMetrics.length > 0) {\n                          // Get the latest metrics\n                          const latestMetrics = videoMetrics[videoMetrics.length - 1];\n                          views = latestMetrics.views || 0;\n                          likes = latestMetrics.likes || 0;\n                          comments = latestMetrics.comments || 0;\n\n                          // Format time series data using our helper function\n                          viewsData = formatTimeSeriesData(videoMetrics.map(m => ({\n                            date: m._time,\n                            views: m.views || 0\n                          })), 'views');\n\n                          likesData = formatTimeSeriesData(videoMetrics.map(m => ({\n                            date: m._time,\n                            likes: m.likes || 0\n                          })), 'likes');\n\n                          commentsData = formatTimeSeriesData(videoMetrics.map(m => ({\n                            date: m._time,\n                            comments: m.comments || 0\n                          })), 'comments');\n                        } else {\n                          // Use zeros for metrics when no data is available\n                          views = 0;\n                          likes = 0;\n                          comments = 0;\n                          viewsData = [];\n                          likesData = [];\n                          commentsData = [];\n                        }\n\n                        videos.push({\n                          id: `video-${resource.id}`,\n                          title: resource.title,\n                          url: resource.url,\n                          thumbnail: thumbnailUrl,\n                          views,\n                          likes,\n                          comments,\n                          viewsData,\n                          likesData,\n                          commentsData\n                        });\n                      } catch (videoError) {\n                        console.error(`Error processing video ${resource.url}:`, videoError);\n                      }\n                    }\n                  }\n\n                  // No need to generate mock videos - if there are no videos with metrics, we'll show empty state\n\n                  // If we don't have metrics from InfluxDB, calculate them from videos\n                  if (!variableMetrics) {\n                    const allViews = videos.map(v => v.views);\n                    const allLikes = videos.map(v => v.likes);\n                    const allComments = videos.map(v => v.comments);\n\n                    // Check if this is a single video\n                    const isSingleVideo = videos.length === 1;\n\n                    // For a single video, we'll use the individual video metrics directly\n                    // For multiple videos, we'll calculate averages and medians\n                    let avgViews, avgLikes, avgComments, medianViews, medianLikes, medianComments;\n\n                    if (isSingleVideo && videos.length > 0) {\n                      // Use the individual video metrics directly\n                      avgViews = videos[0].views;\n                      avgLikes = videos[0].likes;\n                      avgComments = videos[0].comments || 0;\n                      medianViews = videos[0].views;\n                      medianLikes = videos[0].likes;\n                      medianComments = videos[0].comments || 0;\n                    } else {\n                      // Calculate averages and medians for multiple videos\n                      avgViews = allViews.length > 0 ?\n                        Math.round(allViews.reduce((a, b) => a + b, 0) / allViews.length) : 0;\n\n                      avgLikes = allLikes.length > 0 ?\n                        Math.round(allLikes.reduce((a, b) => a + b, 0) / allLikes.length) : 0;\n\n                      avgComments = allComments.length > 0 ?\n                        Math.round(allComments.reduce((a, b) => a + b, 0) / allComments.length) : 0;\n\n                      medianViews = calculateMedian(allViews);\n                      medianLikes = calculateMedian(allLikes);\n                      medianComments = calculateMedian(allComments);\n                    }\n\n                    variableMetrics = {\n                      avgViews,\n                      avgLikes,\n                      avgComments,\n                      medianViews,\n                      medianLikes,\n                      medianComments,\n                      viewsOverTime: [],\n                      likesOverTime: [],\n                      commentsOverTime: [],\n                      isSingleVideo\n                    };\n                  }\n\n                  processedVariables.push({\n                    id: `var-${variable.id}`,\n                    name: variable.name,\n                    note: variable.note || 'No description provided',\n                    videos,\n                    metrics: variableMetrics\n                  });\n                }\n\n                setVariables(processedVariables);\n                setLoading(false);\n              };\n\n              processVariablesWithMetrics();\n            } else {\n              // Fallback to a single variable if no variables found\n              setVariables([{\n                id: 'var-mock',\n                name: 'Sample Variable',\n                note: 'This is a sample variable for testing',\n                videos: [],\n                metrics: {\n                  avgViews: 0,\n                  avgLikes: 0,\n                  medianViews: 0,\n                  medianLikes: 0,\n                  viewsOverTime: [],\n                  likesOverTime: [],\n                  isSingleVideo: true\n                }\n              }]);\n              setLoading(false);\n            }\n          } else {\n            setLoading(false);\n          }\n        } else {\n          // Fallback to localStorage if API fails\n          const savedProjects = localStorage.getItem('projects');\n          const id = params?.id;\n          if (savedProjects && id) {\n            const projects = JSON.parse(savedProjects);\n            const project = projects.find((p: any) => p.id.toString() === id.toString());\n\n            if (project) {\n              setProject(project);\n\n              // Generate mock variable data based on actual project variables\n              const mockVariables = [];\n\n              // If we have actual variables in the project\n              if (project.variables && project.variables.length > 0) {\n                // Create mock data for each actual variable\n                for (let i = 0; i < project.variables.length; i++) {\n                  const variable = project.variables[i];\n                  const mockVideoCount = Math.floor(Math.random() * 3) + 2;\n                  const videos = [];\n\n                  for (let j = 0; j < mockVideoCount; j++) {\n                    const views = Math.floor(Math.random() * 50000) + 5000;\n                    const likes = Math.floor(views * (Math.random() * 0.1 + 0.05));\n\n                    videos.push({\n                      id: `mock-video-${j}`,\n                      title: `Sample YouTube Video ${j + 1}`,\n                      url: `https://youtube.com/watch?v=sample${j}`,\n                      views,\n                      likes,\n                      viewsData: generateMockTimeData(30, views, 0.1),\n                      likesData: generateMockTimeData(30, likes, 0.15)\n                    });\n                  }\n\n                  // Calculate metrics\n                  const allViews = videos.map(v => v.views);\n                  const allLikes = videos.map(v => v.likes);\n\n                  // Check if this is a single video\n                  const isSingleVideo = videos.length === 1;\n\n                  // For a single video, use the individual video metrics directly\n                  let avgViews, avgLikes, medianViews, medianLikes;\n\n                  if (isSingleVideo && videos.length > 0) {\n                    // Use the individual video metrics directly\n                    avgViews = videos[0].views;\n                    avgLikes = videos[0].likes;\n                    medianViews = videos[0].views;\n                    medianLikes = videos[0].likes;\n                  } else {\n                    // Calculate averages and medians for multiple videos\n                    avgViews = Math.round(allViews.reduce((a, b) => a + b, 0) / allViews.length);\n                    avgLikes = Math.round(allLikes.reduce((a, b) => a + b, 0) / allLikes.length);\n                    medianViews = calculateMedian(allViews);\n                    medianLikes = calculateMedian(allLikes);\n                  }\n\n                  mockVariables.push({\n                    id: `var-${i}`,\n                    name: variable.name || `Variable ${i + 1}`,\n                    note: variable.note || 'No description provided',\n                    videos,\n                    metrics: {\n                      avgViews,\n                      avgLikes,\n                      medianViews,\n                      medianLikes,\n                      viewsOverTime: generateMockTimeData(30, avgViews, 0.05),\n                      likesOverTime: generateMockTimeData(30, avgLikes, 0.08),\n                      isSingleVideo\n                    }\n                  });\n                }\n              } else {\n                // Fallback to a single variable if no variables found\n                mockVariables.push({\n                  id: 'var-mock',\n                  name: 'Sample Variable',\n                  note: 'This is a sample variable for testing',\n                  videos: [],\n                  metrics: {\n                    avgViews: 0,\n                    avgLikes: 0,\n                    medianViews: 0,\n                    medianLikes: 0,\n                    viewsOverTime: [],\n                    likesOverTime: [],\n                    isSingleVideo: true\n                  }\n                });\n              }\n\n              setVariables(mockVariables);\n            }\n            setLoading(false);\n          } else {\n            setLoading(false);\n          }\n        }\n      } catch (error) {\n        console.error('Error loading project:', error);\n        setLoading(false);\n      }\n    };\n\n    loadProject();\n  }, [params?.id]);\n\n  // Helper function to extract YouTube video ID from URL\n  const extractYouTubeVideoId = (url: string): string | null => {\n    if (!url) return null;\n\n    const pattern = /(?:youtube\\.com\\/(?:[^\\/]+\\/.+\\/|(?:v|e(?:mbed)?\\/)|.*[?&]v=)|youtu\\.be\\/)([^\"&?\\/\\s]{11})/;\n    const match = url.match(pattern);\n\n    return match ? match[1] : null;\n  };\n\n  const handleBack = () => {\n    router.push('/dashboard');\n  };\n\n  const handleVariableClick = (variable: any) => {\n    setSelectedVariable(variable);\n  };\n\n  const closeVariableDetail = () => {\n    setSelectedVariable(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center h-full\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500\"></div>\n      </div>\n    );\n  }\n\n  if (!project) {\n    return (\n      <div className=\"p-6\">\n        <div className=\"bg-red-50 text-red-500 p-4 rounded-lg\">\n          <h2 className=\"text-lg font-medium\">Project Not Found</h2>\n          <p className=\"mt-2\">The project you're looking for doesn't exist or has been deleted.</p>\n          <button\n            onClick={handleBack}\n            className=\"mt-4 flex items-center text-indigo-600 hover:text-indigo-800\"\n          >\n            <FaArrowLeft className=\"mr-2\" /> Back to Dashboard\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"p-6 space-y-6\">\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <button\n            onClick={handleBack}\n            className=\"flex items-center text-indigo-600 hover:text-indigo-800 mb-2\"\n          >\n            <FaArrowLeft className=\"mr-2\" /> Back to Dashboard\n          </button>\n          <h1 className=\"text-2xl font-bold text-gray-800\">{project.name}</h1>\n          <p className=\"text-gray-500\">Created on {project.dueDate}</p>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-sm text-gray-500\">Total Variables</div>\n          <div className=\"text-2xl font-bold\">{variables.length}</div>\n        </div>\n      </div>\n\n      {/* Variables Grid */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {variables.map((variable) => (\n          <div\n            key={variable.id}\n            className=\"bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer\"\n            onClick={() => handleVariableClick(variable)}\n          >\n            <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 p-4\">\n              <h2 className=\"text-lg font-semibold text-white\">{variable.name}</h2>\n              <p className=\"text-indigo-100 text-sm truncate\">{variable.note}</p>\n            </div>\n\n            <div className=\"p-4\">\n              <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                <div>\n                  <div className=\"text-sm font-medium text-gray-700\">\n                    {variable.metrics.isSingleVideo ? 'Total Views' : 'Avg. Views'}\n                  </div>\n                  <div className=\"text-xl font-bold text-gray-900\">\n                    {variable.metrics.isSingleVideo && variable.videos.length > 0\n                      ? variable.videos[0].views.toLocaleString()\n                      : variable.metrics.avgViews.toLocaleString()}\n                  </div>\n                </div>\n                <div>\n                  <div className=\"text-sm font-medium text-gray-700\">\n                    {variable.metrics.isSingleVideo ? 'Total Likes' : 'Avg. Likes'}\n                  </div>\n                  <div className=\"text-xl font-bold text-gray-900\">\n                    {variable.metrics.isSingleVideo && variable.videos.length > 0\n                      ? variable.videos[0].likes.toLocaleString()\n                      : variable.metrics.avgLikes.toLocaleString()}\n                  </div>\n                </div>\n                <div>\n                  <div className=\"text-sm font-medium text-gray-700\">\n                    {variable.metrics.isSingleVideo ? 'Total Comments' : 'Avg. Comments'}\n                  </div>\n                  <div className=\"text-xl font-bold text-gray-900\">\n                    {variable.metrics.isSingleVideo && variable.videos.length > 0\n                      ? (variable.videos[0].comments?.toLocaleString() || '0')\n                      : (variable.metrics.avgComments?.toLocaleString() || '0')}\n                  </div>\n                </div>\n              </div>\n\n              {!variable.metrics.isSingleVideo && (\n                <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-700\">Median Views</div>\n                    <div className=\"text-xl font-bold text-gray-900\">\n                      {variable.metrics.medianViews.toLocaleString()}\n                    </div>\n                  </div>\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-700\">Median Likes</div>\n                    <div className=\"text-xl font-bold text-gray-900\">\n                      {variable.metrics.medianLikes.toLocaleString()}\n                    </div>\n                  </div>\n                  <div>\n                    <div className=\"text-sm font-medium text-gray-700\">Median Comments</div>\n                    <div className=\"text-xl font-bold text-gray-900\">\n                      {variable.metrics.medianComments?.toLocaleString() || '0'}\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              <div className=\"space-y-4\">\n                <LineChart\n                  data={variable.metrics.viewsOverTime}\n                  label=\"Views Over Time\"\n                  color=\"#4f46e5\"\n                  height={80}\n                />\n                <LineChart\n                  data={variable.metrics.likesOverTime}\n                  label=\"Likes Over Time\"\n                  color=\"#7c3aed\"\n                  height={80}\n                />\n                {variable.metrics.commentsOverTime && variable.metrics.commentsOverTime.length > 0 && (\n                  <LineChart\n                    data={variable.metrics.commentsOverTime}\n                    label=\"Comments Over Time\"\n                    color=\"#10b981\"\n                    height={80}\n                  />\n                )}\n              </div>\n\n              <div className=\"mt-4 flex items-center justify-between text-sm\">\n                <div className=\"text-gray-500\">\n                  <span className=\"font-medium\">{variable.videos.length}</span> videos\n                </div>\n                <div className=\"text-indigo-600 flex items-center\">\n                  View Details <FaChartLine className=\"ml-1\" />\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Variable Detail Modal */}\n      {selectedVariable && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto\">\n          <div className=\"bg-white rounded-xl shadow-xl w-full max-w-4xl my-8\">\n            <div className=\"bg-gradient-to-r from-indigo-500 to-purple-600 p-4 rounded-t-xl flex justify-between items-center\">\n              <div>\n                <h2 className=\"text-xl font-semibold text-white\">{selectedVariable.name}</h2>\n                <p className=\"text-indigo-100 text-sm\">{selectedVariable.note}</p>\n              </div>\n              <button\n                onClick={closeVariableDetail}\n                className=\"text-white hover:text-indigo-200\"\n              >\n                &times;\n              </button>\n            </div>\n\n            <div className=\"p-6 max-h-[80vh] overflow-y-auto\">\n              {selectedVariable.metrics.isSingleVideo ? (\n                // For a single video, show just one box with total metrics\n                // Use the individual video metrics directly instead of the aggregate metrics\n                <div className=\"mb-6\">\n                  <div className=\"bg-indigo-50 p-4 rounded-lg shadow-sm\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-2\">Total Performance</div>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <FaEye className=\"mr-1\" /> Views\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.videos[0]?.views.toLocaleString() || '0'}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <FaThumbsUp className=\"mr-1\" /> Likes\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.videos[0]?.likes.toLocaleString() || '0'}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <span className=\"mr-1\">💬</span> Comments\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.videos[0]?.comments.toLocaleString() || '0'}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ) : (\n                // For multiple videos, show both average and median\n                <div className=\"grid grid-cols-2 gap-6 mb-6\">\n                  <div className=\"bg-indigo-50 p-4 rounded-lg shadow-sm\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-2\">Average Performance</div>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <FaEye className=\"mr-1\" /> Views\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.avgViews.toLocaleString()}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <FaThumbsUp className=\"mr-1\" /> Likes\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.avgLikes.toLocaleString()}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-indigo-700 font-medium\">\n                          <span className=\"mr-1\">💬</span> Comments\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.avgComments?.toLocaleString() || '0'}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"bg-purple-50 p-4 rounded-lg shadow-sm\">\n                    <div className=\"text-sm font-medium text-gray-700 mb-2\">Median Performance</div>\n                    <div className=\"grid grid-cols-3 gap-4\">\n                      <div>\n                        <div className=\"flex items-center text-purple-700 font-medium\">\n                          <FaEye className=\"mr-1\" /> Views\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.medianViews.toLocaleString()}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-purple-700 font-medium\">\n                          <FaThumbsUp className=\"mr-1\" /> Likes\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.medianLikes.toLocaleString()}\n                        </div>\n                      </div>\n                      <div>\n                        <div className=\"flex items-center text-purple-700 font-medium\">\n                          <span className=\"mr-1\">💬</span> Comments\n                        </div>\n                        <div className=\"text-2xl font-bold text-gray-900\">\n                          {selectedVariable.metrics.medianComments?.toLocaleString() || '0'}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n\n              <h3 className=\"text-lg font-medium text-gray-800 mb-4\">Individual Videos</h3>\n\n              <div className=\"space-y-4\">\n                {selectedVariable.videos.map((video: any) => (\n                  <div key={video.id} className=\"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\">\n                    <div className=\"flex flex-col md:flex-row gap-4\">\n                      <div className=\"md:w-1/3\">\n                        <div className=\"bg-gray-200 rounded-lg aspect-video flex items-center justify-center overflow-hidden\">\n                          {video.thumbnail ? (\n                            <img\n                              src={video.thumbnail}\n                              alt={video.title}\n                              className=\"w-full h-full object-cover\"\n                            />\n                          ) : (\n                            <FaYoutube className=\"text-red-600 text-4xl\" />\n                          )}\n                        </div>\n                        <div className=\"mt-2\">\n                          <h4 className=\"font-medium text-gray-800\">{video.title}</h4>\n                          <a\n                            href={video.url}\n                            target=\"_blank\"\n                            rel=\"noopener noreferrer\"\n                            className=\"text-sm text-blue-600 hover:underline flex items-center mt-1\"\n                            onClick={(e) => e.stopPropagation()}\n                          >\n                            <FaLink className=\"mr-1\" /> View on YouTube\n                          </a>\n                        </div>\n                      </div>\n\n                      <div className=\"md:w-2/3\">\n                        <div className=\"grid grid-cols-3 gap-4 mb-4\">\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-700\">Total Views</div>\n                            <div className=\"text-xl font-bold text-gray-900\">\n                              {video.views.toLocaleString()}\n                            </div>\n                          </div>\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-700\">Total Likes</div>\n                            <div className=\"text-xl font-bold text-gray-900\">\n                              {video.likes.toLocaleString()}\n                            </div>\n                          </div>\n                          <div>\n                            <div className=\"text-sm font-medium text-gray-700\">Total Comments</div>\n                            <div className=\"text-xl font-bold text-gray-900\">\n                              {video.comments.toLocaleString()}\n                            </div>\n                          </div>\n                        </div>\n\n                        <div className=\"space-y-4\">\n                          <LineChart\n                            data={video.viewsData}\n                            label=\"Views Over Time\"\n                            color=\"#4f46e5\"\n                            height={100}\n                          />\n                          <LineChart\n                            data={video.likesData}\n                            label=\"Likes Over Time\"\n                            color=\"#7c3aed\"\n                            height={100}\n                          />\n                          <LineChart\n                            data={video.commentsData}\n                            label=\"Comments Over Time\"\n                            color=\"#10b981\"\n                            height={100}\n                          />\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAQA;AACA;AAbA;;;;;;;AAeA,wDAAwD;AACxD,MAAM,uBAAuB,CAAC,MAAM,QAAQ,OAAO;IACjD,IAAI,CAAC,QAAQ,CAAC,KAAK,MAAM,EAAE;QACzB,gCAAgC;QAChC,OAAO,EAAE;IACX;IAEA,IAAI;QACF,iDAAiD;QACjD,MAAM,QAAQ,KAAK,GAAG,CAAC,CAAA,OAAQ,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI;QACzD,MAAM,aAAa,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,CAAC,CAAA,OACjD,KAAK,OAAO,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO,MACnC,KAAK,QAAQ,OAAO,KAAK,CAAC,EAAE,CAAC,QAAQ,MACrC,KAAK,WAAW,OAAO,KAAK,CAAC,EAAE,CAAC,WAAW;QAG7C,sFAAsF;QACtF,MAAM,eAAe,AAAC,cAAc,MAAM,MAAM,GAAG,IAAK,WAAW;QAEnE,OAAO,KAAK,GAAG,CAAC,CAAA,OAAQ,CAAC;gBACvB,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,IAAI,IAAI,IAAI,SAAS;gBAChD,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,IAAI;YACnC,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sCAAsC,EAAE,MAAM,CAAC,CAAC,EAAE;QACjE,OAAO,EAAE;IACX;AACF;AAEA,sCAAsC;AACtC,MAAM,kBAAkB,CAAC;IACvB,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE,OAAO;IAEtC,MAAM,SAAS;WAAI;KAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAC9C,MAAM,SAAS,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG;IAE1C,IAAI,OAAO,MAAM,GAAG,MAAM,GAAG;QAC3B,OAAO,KAAK,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,IAAI;IAC5D;IAEA,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,OAAO;AAClC;AAEA,qDAAqD;AACrD,MAAM,oBAAoB,OAAO;IAC/B,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACvC,OAAO,EAAE;IACX;IAEA,IAAI;QACF,MAAM,aAAa,mBAAmB;QACtC,MAAM,WAAW,MAAM,MAAM,CAAC,iCAAiC,EAAE,YAAY;QAC7E,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,OAAO,IAAI,EAAE;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC/D,OAAO,EAAE;IACX;AACF;AAEA,kDAAkD;AAClD,MAAM,sBAAsB,OAAO;IACjC,IAAI,CAAC,YAAY,SAAS,IAAI,OAAO,IAAI;QACvC,OAAO;IACT;IAEA,IAAI;QACF,MAAM,aAAa,mBAAmB;QACtC,MAAM,WAAW,MAAM,MAAM,CAAC,qCAAqC,EAAE,YAAY;QACjF,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,YAAY,IAAI;IAC9B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,SAAS,CAAC,CAAC,EAAE;QACjE,OAAO;IACT;AACF;AAEA,8DAA8D;AAC9D,MAAM,sBAAsB,OAAO,WAAW,aAAa,IAAI;IAC7D,IAAI;QACF,IAAI,MAAM,CAAC,+BAA+B,EAAE,WAAW;QACvD,IAAI,YAAY;YACd,OAAO,CAAC,YAAY,EAAE,YAAY;QACpC;QAEA,MAAM,WAAW,MAAM,MAAM;QAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,sFAAsF;QACtF,MAAM,gBAAgB,KAAK,gBAAgB,EAAE,iBAAiB;QAE9D,OAAO;YACL,UAAU,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,OAAO,OAAO;YAC1D,UAAU,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,OAAO,OAAO;YAC1D,aAAa,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,UAAU,OAAO;YAChE,aAAa,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,OAAO,UAAU;YAChE,aAAa,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,OAAO,UAAU;YAChE,gBAAgB,KAAK,KAAK,CAAC,KAAK,gBAAgB,EAAE,UAAU,UAAU;YACtE,eAAe,qBAAqB,KAAK,UAAU,IAAI,EAAE,EAAE;YAC3D,eAAe,qBAAqB,KAAK,UAAU,IAAI,EAAE,EAAE;YAC3D,kBAAkB,qBAAqB,KAAK,UAAU,IAAI,EAAE,EAAE;YAC9D,SAAS,KAAK,OAAO,IAAI,EAAE;YAC3B,eAAe;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,4CAA4C,CAAC,EAAE;QAC9D,OAAO;YACL,UAAU;YACV,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,eAAe,EAAE;YACjB,eAAe,EAAE;YACjB,kBAAkB,EAAE;YACpB,SAAS,EAAE;YACX,eAAe;QACjB;IACF;AACF;AAEA,wDAAwD;AACxD,MAAM,uBAAuB,OAAO;IAClC,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM,EAAE;QACnC,OAAO;YACL,UAAU;YACV,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,eAAe,EAAE;YACjB,eAAe,EAAE;YACjB,kBAAkB,EAAE;YACpB,eAAe;QACjB;IACF;IAEA,IAAI;QACF,kCAAkC;QAClC,MAAM,gBAAgB,UAAU,MAAM,KAAK;QAE3C,kBAAkB;QAClB,MAAM,cAAc,UAAU,GAAG,CAAC,CAAA,MAAO,mBAAmB;QAE5D,0BAA0B;QAC1B,MAAM,oBAAoB,MAAM,MAAM,CAAC,sCAAsC,EAAE,YAAY,IAAI,CAAC,MAAM;QACtG,MAAM,gBAAgB,MAAM,kBAAkB,IAAI;QAElD,yBAAyB;QACzB,MAAM,qBAAqB,MAAM,MAAM,CAAC,uCAAuC,EAAE,YAAY,IAAI,CAAC,MAAM;QACxG,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;QAEpD,8CAA8C;QAC9C,MAAM,mBAAmB,MAAM,MAAM,CAAC,qCAAqC,EAAE,YAAY,IAAI,CAAC,MAAM;QACpG,MAAM,eAAe,MAAM,iBAAiB,IAAI;QAEhD,mDAAmD;QACnD,OAAO;YACL,UAAU,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,OAAO,OAAO;YAC1D,UAAU,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,OAAO,OAAO;YAC1D,aAAa,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,UAAU,OAAO;YAChE,aAAa,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,OAAO,UAAU;YAChE,aAAa,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,OAAO,UAAU;YAChE,gBAAgB,KAAK,KAAK,CAAC,cAAc,OAAO,EAAE,UAAU,UAAU;YACtE,eAAe,qBAAqB,eAAe,UAAU,IAAI,EAAE,EAAE;YACrE,eAAe,qBAAqB,eAAe,UAAU,IAAI,EAAE,EAAE;YACrE,kBAAkB,qBAAqB,eAAe,UAAU,IAAI,EAAE,EAAE;YACxE,eAAe,iBAAiB,cAAc,OAAO,EAAE,iBAAiB;QAC1E;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,CAAC,EAAE;QACtD,OAAO;YACL,UAAU;YACV,UAAU;YACV,aAAa;YACb,aAAa;YACb,aAAa;YACb,gBAAgB;YAChB,eAAe,EAAE;YACjB,eAAe,EAAE;YACjB,kBAAkB,EAAE;YACpB,eAAe,UAAU,MAAM,KAAK;QACtC;IACF;AACF;AAEA,kDAAkD;AAClD,MAAM,sBAAsB,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,CAAC,uBAAuB,EAAE,YAAY;QACnE,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;QAElC,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,kEAAkE;QAClE,OAAO,KAAK,MAAM,CAAC,GAAG,CAAC,CAAA,QAAS,CAAC;gBAC/B,IAAI,MAAM,EAAE;gBACZ,KAAK,MAAM,GAAG;gBACd,OAAO,MAAM,KAAK,IAAI,CAAC,cAAc,EAAE,MAAM,EAAE,EAAE;gBACjD,SAAS,MAAM,OAAO;gBACtB,WAAW,MAAM,SAAS;gBAC1B,YAAY,MAAM,UAAU;gBAC5B,WAAW,MAAM,SAAS;YAC5B,CAAC;IACH,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,EAAE;QACnE,OAAO,EAAE;IACX;AACF;AAEA,iEAAiE;AACjE,MAAM,uBAAuB,CAAC,OAAO,EAAE,EAAE,YAAY,IAAI,EAAE,aAAa,GAAG;IACzE,MAAM,OAAO,EAAE;IACf,IAAI,eAAe;IAEnB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;QAC7B,sCAAsC;QACtC,MAAM,OAAO,IAAI;QACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvC,8BAA8B;QAC9B,MAAM,SAAS,eAAe,CAAC,KAAK,MAAM,KAAK,aAAa,IAAI,UAAU;QAC1E,eAAe,KAAK,GAAG,CAAC,GAAG,eAAe;QAE1C,KAAK,IAAI,CAAC;YACR,MAAM,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;YACnB,OAAO,KAAK,KAAK,CAAC;QACpB;IACF;IAEA,OAAO;AACT;AAEA,gFAAgF;AAChF,MAAM,YAAY,uIAAA,CAAA,UAAiB;AAEpB,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iCAAiC;QACjC,MAAM,cAAc;YAClB,IAAI;gBACF,yBAAyB;gBACzB,MAAM,KAAK,QAAQ;gBACnB,IAAI,CAAC,IAAI;gBAET,4BAA4B;gBAC5B,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI;gBAElD,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,UAAU,KAAK,OAAO;oBAE5B,IAAI,SAAS;wBACX,0BAA0B;wBAC1B,MAAM,mBAAmB;4BACvB,IAAI,QAAQ,EAAE;4BACd,MAAM,QAAQ,IAAI;4BAClB,aAAa,QAAQ,WAAW;4BAChC,SAAS,IAAI,KAAK,QAAQ,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;4BACjE,WAAW,QAAQ,UAAU;4BAC7B,SAAS,QAAQ,OAAO,IAAI;wBAC9B;wBAEA,WAAW;wBAEX,wCAAwC;wBACxC,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;4BACrD,wBAAwB;4BACxB,MAAM,8BAA8B;gCAClC,MAAM,qBAAqB,EAAE;gCAE7B,KAAK,MAAM,YAAY,QAAQ,SAAS,CAAE;oCACxC,4DAA4D;oCAC5D,yEAAyE;oCACzE,MAAM,mBAAmB,MAAM,oBAAoB,SAAS,EAAE;oCAE9D,4DAA4D;oCAC5D,IAAI,kBAAkB;oCACtB,IAAI,SAAS,EAAE;oCAEf,IAAI,iBAAiB,MAAM,GAAG,GAAG;wCAC/B,sCAAsC;wCACtC,MAAM,YAAY,iBAAiB,GAAG,CAAC,CAAA,IAAK,EAAE,GAAG;wCAEjD,uEAAuE;wCACvE,kBAAkB,MAAM,oBAAoB,QAAQ,EAAE,EAAE,SAAS,EAAE;wCAEnE,+CAA+C;wCAC/C,KAAK,MAAM,YAAY,iBAAkB;4CACvC,IAAI;gDACF,wCAAwC;gDACxC,MAAM,eAAe,MAAM,kBAAkB,SAAS,GAAG;gDAEzD,iCAAiC;gDACjC,MAAM,eAAe,MAAM,oBAAoB,SAAS,GAAG;gDAE3D,oDAAoD;gDACpD,IAAI,QAAQ;gDACZ,IAAI,QAAQ;gDACZ,IAAI,WAAW;gDACf,IAAI,YAAY,EAAE;gDAClB,IAAI,YAAY,EAAE;gDAClB,IAAI,eAAe,EAAE;gDAErB,IAAI,gBAAgB,aAAa,MAAM,GAAG,GAAG;oDAC3C,yBAAyB;oDACzB,MAAM,gBAAgB,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE;oDAC3D,QAAQ,cAAc,KAAK,IAAI;oDAC/B,QAAQ,cAAc,KAAK,IAAI;oDAC/B,WAAW,cAAc,QAAQ,IAAI;oDAErC,oDAAoD;oDACpD,YAAY,qBAAqB,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC;4DACtD,MAAM,EAAE,KAAK;4DACb,OAAO,EAAE,KAAK,IAAI;wDACpB,CAAC,IAAI;oDAEL,YAAY,qBAAqB,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC;4DACtD,MAAM,EAAE,KAAK;4DACb,OAAO,EAAE,KAAK,IAAI;wDACpB,CAAC,IAAI;oDAEL,eAAe,qBAAqB,aAAa,GAAG,CAAC,CAAA,IAAK,CAAC;4DACzD,MAAM,EAAE,KAAK;4DACb,UAAU,EAAE,QAAQ,IAAI;wDAC1B,CAAC,IAAI;gDACP,OAAO;oDACL,kDAAkD;oDAClD,QAAQ;oDACR,QAAQ;oDACR,WAAW;oDACX,YAAY,EAAE;oDACd,YAAY,EAAE;oDACd,eAAe,EAAE;gDACnB;gDAEA,OAAO,IAAI,CAAC;oDACV,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE;oDAC1B,OAAO,SAAS,KAAK;oDACrB,KAAK,SAAS,GAAG;oDACjB,WAAW;oDACX;oDACA;oDACA;oDACA;oDACA;oDACA;gDACF;4CACF,EAAE,OAAO,YAAY;gDACnB,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE;4CAC3D;wCACF;oCACF;oCAEA,gGAAgG;oCAEhG,qEAAqE;oCACrE,IAAI,CAAC,iBAAiB;wCACpB,MAAM,WAAW,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;wCACxC,MAAM,WAAW,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;wCACxC,MAAM,cAAc,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;wCAE9C,kCAAkC;wCAClC,MAAM,gBAAgB,OAAO,MAAM,KAAK;wCAExC,sEAAsE;wCACtE,4DAA4D;wCAC5D,IAAI,UAAU,UAAU,aAAa,aAAa,aAAa;wCAE/D,IAAI,iBAAiB,OAAO,MAAM,GAAG,GAAG;4CACtC,4CAA4C;4CAC5C,WAAW,MAAM,CAAC,EAAE,CAAC,KAAK;4CAC1B,WAAW,MAAM,CAAC,EAAE,CAAC,KAAK;4CAC1B,cAAc,MAAM,CAAC,EAAE,CAAC,QAAQ,IAAI;4CACpC,cAAc,MAAM,CAAC,EAAE,CAAC,KAAK;4CAC7B,cAAc,MAAM,CAAC,EAAE,CAAC,KAAK;4CAC7B,iBAAiB,MAAM,CAAC,EAAE,CAAC,QAAQ,IAAI;wCACzC,OAAO;4CACL,qDAAqD;4CACrD,WAAW,SAAS,MAAM,GAAG,IAC3B,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,SAAS,MAAM,IAAI;4CAEtE,WAAW,SAAS,MAAM,GAAG,IAC3B,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,SAAS,MAAM,IAAI;4CAEtE,cAAc,YAAY,MAAM,GAAG,IACjC,KAAK,KAAK,CAAC,YAAY,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,YAAY,MAAM,IAAI;4CAE5E,cAAc,gBAAgB;4CAC9B,cAAc,gBAAgB;4CAC9B,iBAAiB,gBAAgB;wCACnC;wCAEA,kBAAkB;4CAChB;4CACA;4CACA;4CACA;4CACA;4CACA;4CACA,eAAe,EAAE;4CACjB,eAAe,EAAE;4CACjB,kBAAkB,EAAE;4CACpB;wCACF;oCACF;oCAEA,mBAAmB,IAAI,CAAC;wCACtB,IAAI,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;wCACxB,MAAM,SAAS,IAAI;wCACnB,MAAM,SAAS,IAAI,IAAI;wCACvB;wCACA,SAAS;oCACX;gCACF;gCAEA,aAAa;gCACb,WAAW;4BACb;4BAEA;wBACF,OAAO;4BACL,sDAAsD;4BACtD,aAAa;gCAAC;oCACZ,IAAI;oCACJ,MAAM;oCACN,MAAM;oCACN,QAAQ,EAAE;oCACV,SAAS;wCACP,UAAU;wCACV,UAAU;wCACV,aAAa;wCACb,aAAa;wCACb,eAAe,EAAE;wCACjB,eAAe,EAAE;wCACjB,eAAe;oCACjB;gCACF;6BAAE;4BACF,WAAW;wBACb;oBACF,OAAO;wBACL,WAAW;oBACb;gBACF,OAAO;oBACL,wCAAwC;oBACxC,MAAM,gBAAgB,aAAa,OAAO,CAAC;oBAC3C,MAAM,KAAK,QAAQ;oBACnB,IAAI,iBAAiB,IAAI;wBACvB,MAAM,WAAW,KAAK,KAAK,CAAC;wBAC5B,MAAM,UAAU,SAAS,IAAI,CAAC,CAAC,IAAW,EAAE,EAAE,CAAC,QAAQ,OAAO,GAAG,QAAQ;wBAEzE,IAAI,SAAS;4BACX,WAAW;4BAEX,gEAAgE;4BAChE,MAAM,gBAAgB,EAAE;4BAExB,6CAA6C;4BAC7C,IAAI,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;gCACrD,4CAA4C;gCAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,SAAS,CAAC,MAAM,EAAE,IAAK;oCACjD,MAAM,WAAW,QAAQ,SAAS,CAAC,EAAE;oCACrC,MAAM,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK;oCACvD,MAAM,SAAS,EAAE;oCAEjB,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,IAAK;wCACvC,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;wCAClD,MAAM,QAAQ,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,MAAM,KAAK,MAAM,IAAI;wCAE5D,OAAO,IAAI,CAAC;4CACV,IAAI,CAAC,WAAW,EAAE,GAAG;4CACrB,OAAO,CAAC,qBAAqB,EAAE,IAAI,GAAG;4CACtC,KAAK,CAAC,kCAAkC,EAAE,GAAG;4CAC7C;4CACA;4CACA,WAAW,qBAAqB,IAAI,OAAO;4CAC3C,WAAW,qBAAqB,IAAI,OAAO;wCAC7C;oCACF;oCAEA,oBAAoB;oCACpB,MAAM,WAAW,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;oCACxC,MAAM,WAAW,OAAO,GAAG,CAAC,CAAA,IAAK,EAAE,KAAK;oCAExC,kCAAkC;oCAClC,MAAM,gBAAgB,OAAO,MAAM,KAAK;oCAExC,gEAAgE;oCAChE,IAAI,UAAU,UAAU,aAAa;oCAErC,IAAI,iBAAiB,OAAO,MAAM,GAAG,GAAG;wCACtC,4CAA4C;wCAC5C,WAAW,MAAM,CAAC,EAAE,CAAC,KAAK;wCAC1B,WAAW,MAAM,CAAC,EAAE,CAAC,KAAK;wCAC1B,cAAc,MAAM,CAAC,EAAE,CAAC,KAAK;wCAC7B,cAAc,MAAM,CAAC,EAAE,CAAC,KAAK;oCAC/B,OAAO;wCACL,qDAAqD;wCACrD,WAAW,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,SAAS,MAAM;wCAC3E,WAAW,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,SAAS,MAAM;wCAC3E,cAAc,gBAAgB;wCAC9B,cAAc,gBAAgB;oCAChC;oCAEA,cAAc,IAAI,CAAC;wCACjB,IAAI,CAAC,IAAI,EAAE,GAAG;wCACd,MAAM,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE,IAAI,GAAG;wCAC1C,MAAM,SAAS,IAAI,IAAI;wCACvB;wCACA,SAAS;4CACP;4CACA;4CACA;4CACA;4CACA,eAAe,qBAAqB,IAAI,UAAU;4CAClD,eAAe,qBAAqB,IAAI,UAAU;4CAClD;wCACF;oCACF;gCACF;4BACF,OAAO;gCACL,sDAAsD;gCACtD,cAAc,IAAI,CAAC;oCACjB,IAAI;oCACJ,MAAM;oCACN,MAAM;oCACN,QAAQ,EAAE;oCACV,SAAS;wCACP,UAAU;wCACV,UAAU;wCACV,aAAa;wCACb,aAAa;wCACb,eAAe,EAAE;wCACjB,eAAe,EAAE;wCACjB,eAAe;oCACjB;gCACF;4BACF;4BAEA,aAAa;wBACf;wBACA,WAAW;oBACb,OAAO;wBACL,WAAW;oBACb;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC,QAAQ;KAAG;IAEf,uDAAuD;IACvD,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,KAAK,OAAO;QAEjB,MAAM,UAAU;QAChB,MAAM,QAAQ,IAAI,KAAK,CAAC;QAExB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB;IACtB;IAEA,MAAM,sBAAsB;QAC1B,oBAAoB;IACtB;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,8OAAC;wBAAE,WAAU;kCAAO;;;;;;kCACpB,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BAAS;;;;;;;;;;;;;;;;;;IAK1C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC,8IAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAS;;;;;;;0CAElC,8OAAC;gCAAG,WAAU;0CAAoC,QAAQ,IAAI;;;;;;0CAC9D,8OAAC;gCAAE,WAAU;;oCAAgB;oCAAY,QAAQ,OAAO;;;;;;;;;;;;;kCAE1D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;0CACvC,8OAAC;gCAAI,WAAU;0CAAsB,UAAU,MAAM;;;;;;;;;;;;;;;;;;0BAKzD,8OAAC;gBAAI,WAAU;0BACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,oBAAoB;;0CAEnC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC,SAAS,IAAI;;;;;;kDAC/D,8OAAC;wCAAE,WAAU;kDAAoC,SAAS,IAAI;;;;;;;;;;;;0CAGhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,GAAG,gBAAgB;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IACxD,SAAS,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,KACvC,SAAS,OAAO,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;0DAGhD,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,GAAG,gBAAgB;;;;;;kEAEpD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IACxD,SAAS,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,KACvC,SAAS,OAAO,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;0DAGhD,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,GAAG,mBAAmB;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,aAAa,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG,IACvD,SAAS,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,oBAAoB,MACjD,SAAS,OAAO,CAAC,WAAW,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;oCAK5D,CAAC,SAAS,OAAO,CAAC,aAAa,kBAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;0DAGhD,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;0DAGhD,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,OAAO,CAAC,cAAc,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;kDAM9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAM,SAAS,OAAO,CAAC,aAAa;gDACpC,OAAM;gDACN,OAAM;gDACN,QAAQ;;;;;;0DAEV,8OAAC;gDACC,MAAM,SAAS,OAAO,CAAC,aAAa;gDACpC,OAAM;gDACN,OAAM;gDACN,QAAQ;;;;;;4CAET,SAAS,OAAO,CAAC,gBAAgB,IAAI,SAAS,OAAO,CAAC,gBAAgB,CAAC,MAAM,GAAG,mBAC/E,8OAAC;gDACC,MAAM,SAAS,OAAO,CAAC,gBAAgB;gDACvC,OAAM;gDACN,OAAM;gDACN,QAAQ;;;;;;;;;;;;kDAKd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAe,SAAS,MAAM,CAAC,MAAM;;;;;;oDAAQ;;;;;;;0DAE/D,8OAAC;gDAAI,WAAU;;oDAAoC;kEACpC,8OAAC,8IAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;uBA9FrC,SAAS,EAAE;;;;;;;;;;YAuGrB,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAoC,iBAAiB,IAAI;;;;;;sDACvE,8OAAC;4CAAE,WAAU;sDAA2B,iBAAiB,IAAI;;;;;;;;;;;;8CAE/D,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;sCAKH,8OAAC;4BAAI,WAAU;;gCACZ,iBAAiB,OAAO,CAAC,aAAa,GACrC,2DAA2D;gCAC3D,6EAA6E;8CAC7E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8IAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAS;;;;;;;0EAE5B,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,MAAM,CAAC,EAAE,EAAE,MAAM,oBAAoB;;;;;;;;;;;;kEAG3D,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,8IAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;oEAAS;;;;;;;0EAEjC,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,MAAM,CAAC,EAAE,EAAE,MAAM,oBAAoB;;;;;;;;;;;;kEAG3D,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAO;;;;;;oEAAS;;;;;;;0EAElC,8OAAC;gEAAI,WAAU;0EACZ,iBAAiB,MAAM,CAAC,EAAE,EAAE,SAAS,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;2CAOpE,oDAAoD;8CACpD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAS;;;;;;;8EAE5B,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;sEAGrD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAS;;;;;;;8EAEjC,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,QAAQ,CAAC,cAAc;;;;;;;;;;;;sEAGrD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAO;;;;;;wEAAS;;;;;;;8EAElC,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,WAAW,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;sDAMnE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAyC;;;;;;8DACxD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAS;;;;;;;8EAE5B,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;sEAGxD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,8IAAA,CAAA,aAAU;4EAAC,WAAU;;;;;;wEAAS;;;;;;;8EAEjC,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,WAAW,CAAC,cAAc;;;;;;;;;;;;sEAGxD,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAO;;;;;;wEAAS;;;;;;;8EAElC,8OAAC;oEAAI,WAAU;8EACZ,iBAAiB,OAAO,CAAC,cAAc,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQ1E,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAEvD,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB,MAAM,CAAC,GAAG,CAAC,CAAC,sBAC5B,8OAAC;4CAAmB,WAAU;sDAC5B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,MAAM,SAAS,iBACd,8OAAC;oEACC,KAAK,MAAM,SAAS;oEACpB,KAAK,MAAM,KAAK;oEAChB,WAAU;;;;;yFAGZ,8OAAC,8IAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;0EAGzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAA6B,MAAM,KAAK;;;;;;kFACtD,8OAAC;wEACC,MAAM,MAAM,GAAG;wEACf,QAAO;wEACP,KAAI;wEACJ,WAAU;wEACV,SAAS,CAAC,IAAM,EAAE,eAAe;;0FAEjC,8OAAC,8IAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;4EAAS;;;;;;;;;;;;;;;;;;;kEAKjC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAoC;;;;;;0FACnD,8OAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,cAAc;;;;;;;;;;;;kFAG/B,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAoC;;;;;;0FACnD,8OAAC;gFAAI,WAAU;0FACZ,MAAM,KAAK,CAAC,cAAc;;;;;;;;;;;;kFAG/B,8OAAC;;0FACC,8OAAC;gFAAI,WAAU;0FAAoC;;;;;;0FACnD,8OAAC;gFAAI,WAAU;0FACZ,MAAM,QAAQ,CAAC,cAAc;;;;;;;;;;;;;;;;;;0EAKpC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,MAAM,MAAM,SAAS;wEACrB,OAAM;wEACN,OAAM;wEACN,QAAQ;;;;;;kFAEV,8OAAC;wEACC,MAAM,MAAM,SAAS;wEACrB,OAAM;wEACN,OAAM;wEACN,QAAQ;;;;;;kFAEV,8OAAC;wEACC,MAAM,MAAM,YAAY;wEACxB,OAAM;wEACN,OAAM;wEACN,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;2CAnER,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFpC", "debugId": null}}]}