module.exports = {

"[project]/.next-internal/server/app/api/ab-projects/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[project]/src/app/api/ab-projects/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
([__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__);
;
;
;
// Reuse the existing pool configuration
const getPoolConfig = ()=>{
    const config = {
        host: '***********',
        database: process.env.DB_NAME || 'postgres',
        user: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASS || 'Plotpointe!@3456',
        port: 5432,
        ssl: {
            rejectUnauthorized: false
        }
    };
    return config;
};
// Create the connection pool
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"](getPoolConfig());
// Add error handler
pool.on('error', (err)=>{
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
});
// AB Testing specific Trello list ID
const abAutoApprovedListID = "682c292de90489812322e7ea"; // Replace with your AB testing list ID
async function POST(request) {
    try {
        const projectData = await request.json();
        // Validate input
        if (!projectData.name) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Project name is required'
            }, {
                status: 400
            });
        }
        // Connect to PostgreSQL
        const client = await pool.connect();
        try {
            // Start a transaction
            await client.query('BEGIN');
            // Insert project into AB table
            const projectResult = await client.query(`INSERT INTO ab_src_projects (name, description, members)
         VALUES ($1, $2, $3)
         RETURNING id`, [
                projectData.name,
                projectData.description || '',
                1 // Default members count
            ]);
            const projectId = projectResult.rows[0].id;
            // Insert variables and resources
            if (projectData.variables && projectData.variables.length > 0) {
                for (const variable of projectData.variables){
                    if (!variable.name) continue;
                    const variableResult = await client.query(`INSERT INTO ab_src_project_variables (project_id, name, note)
             VALUES ($1, $2, $3)
             RETURNING id`, [
                        projectId,
                        variable.name,
                        variable.note || ''
                    ]);
                    const variableId = variableResult.rows[0].id;
                    // Insert resources
                    if (variable.resources && variable.resources.length > 0) {
                        for (const resource of variable.resources){
                            if (!resource.googleDocLink && !resource.youtubeUrl) continue;
                            // Insert into ab_variable_resources
                            await client.query(`INSERT INTO ab_variable_resources (variable_id, google_doc_link, youtube_url, note, account)
                 VALUES ($1, $2, $3, $4, $5)
                 RETURNING id`, [
                                variableId,
                                resource.googleDocLink || '',
                                resource.youtubeUrl || '',
                                resource.note || '',
                                resource.account || ''
                            ]);
                            // Get the account_id from the posting_accounts table (reuse existing table)
                            let accountId = null;
                            try {
                                const accountResult = await client.query(`SELECT id FROM posting_accounts WHERE account = $1`, [
                                    resource.account || 'clay'
                                ] // Default to 'clay' if not specified
                                );
                                if (accountResult.rows.length > 0) {
                                    accountId = accountResult.rows[0].id;
                                }
                            } catch (error) {
                                console.error('Error getting account ID:', error);
                            // Continue without account_id if there's an error
                            }
                            // If it's a YouTube URL, check if it already exists in the ab_video table
                            if (resource.youtubeUrl && resource.youtubeUrl.trim() !== '') {
                                // Check if the YouTube URL already exists in the ab_video table
                                const videoCheckResult = await client.query(`SELECT id, writer_id, account_id FROM ab_video WHERE url = $1`, [
                                    resource.youtubeUrl
                                ]);
                                if (videoCheckResult.rows.length > 0) {
                                    // URL exists, check if it has a writer_id and account_id
                                    const existingWriterId = videoCheckResult.rows[0].writer_id;
                                    const existingAccountId = videoCheckResult.rows[0].account_id;
                                    if (existingWriterId && existingAccountId) {
                                        // If both writer_id and account_id exist, just update project_id and variable_id
                                        await client.query(`UPDATE ab_video
                       SET project_id = $1, variable_id = $2
                       WHERE url = $3`, [
                                            projectId,
                                            variableId,
                                            resource.youtubeUrl
                                        ]);
                                    } else if (existingWriterId && !existingAccountId) {
                                        // If writer_id exists but no account_id, update with new account_id
                                        await client.query(`UPDATE ab_video
                       SET project_id = $1, variable_id = $2, account_id = $3
                       WHERE url = $4`, [
                                            projectId,
                                            variableId,
                                            accountId || 1,
                                            resource.youtubeUrl
                                        ]);
                                    } else if (!existingWriterId && existingAccountId) {
                                        // If account_id exists but no writer_id, update with writer_id = 129
                                        await client.query(`UPDATE ab_video
                       SET project_id = $1, variable_id = $2, writer_id = $3
                       WHERE url = $4`, [
                                            projectId,
                                            variableId,
                                            129,
                                            resource.youtubeUrl
                                        ]);
                                    } else {
                                        // If neither exists, update both
                                        await client.query(`UPDATE ab_video
                       SET project_id = $1, variable_id = $2, writer_id = $3, account_id = $4
                       WHERE url = $5`, [
                                            projectId,
                                            variableId,
                                            129,
                                            accountId || 1,
                                            resource.youtubeUrl
                                        ]);
                                    }
                                } else {
                                    // URL doesn't exist, insert it into the ab_video table with the actual variable_id
                                    await client.query(`INSERT INTO ab_video (url, account_id, created, project_id, variable_id, writer_id)
                     VALUES ($1, $2, CURRENT_TIMESTAMP, $3, $4, $5)`, [
                                        resource.youtubeUrl,
                                        accountId || 1,
                                        projectId,
                                        variableId,
                                        129 // Always set writer_id to 129
                                    ]);
                                }
                            }
                            // Only create a Trello card if there's a Google Doc link AND no YouTube link
                            if (resource.googleDocLink && resource.googleDocLink.trim() !== '' && (!resource.youtubeUrl || resource.youtubeUrl.trim() === '')) {
                                try {
                                    // Get the resource index within this variable
                                    const resourceIndex = variable.resources.indexOf(resource) + 1;
                                    // Create a title in the format: {project name}: {variable name}:{resource number}
                                    const cardTitle = `AB-${projectData.name}: ${variable.name}:${resourceIndex}`;
                                    try {
                                        // Fetch Trello settings (reuse existing table)
                                        const settingsResult = await client.query("SELECT api_key, token FROM settings ORDER BY id DESC LIMIT 1");
                                        if (settingsResult.rows.length === 0) {
                                            console.error('Trello settings not configured');
                                            return;
                                        }
                                        const { api_key: apiKey, token: apiToken } = settingsResult.rows[0];
                                        // Create the Trello card directly using the AB list ID
                                        const cardResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post('https://api.trello.com/1/cards', null, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken,
                                                idList: abAutoApprovedListID,
                                                name: cardTitle,
                                                desc: `Google Doc Link: ${resource.googleDocLink}`,
                                                pos: 'top'
                                            }
                                        });
                                        const trelloCardId = cardResponse.data.id;
                                        // Add attachment
                                        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].post(`https://api.trello.com/1/cards/${trelloCardId}/attachments`, null, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken,
                                                url: resource.googleDocLink
                                            }
                                        });
                                        // Discover board and custom-field IDs (for Posting Account)
                                        const cardDetails = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`https://api.trello.com/1/cards/${trelloCardId}`, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken
                                            }
                                        });
                                        const boardId = cardDetails.data.idBoard;
                                        const customFields = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`https://api.trello.com/1/boards/${boardId}/customFields`, {
                                            params: {
                                                key: apiKey,
                                                token: apiToken
                                            }
                                        });
                                        const postingAccountField = customFields.data.find((f)=>f.name === "Posting Account");
                                        let postingAccount = resource.account || 'clay';
                                        // Check if posting account is 'Auto Assign'
                                        if (postingAccount === 'Auto Assign') {
                                            try {
                                                // Call the API to get a posting account
                                                const postingAccountResponse = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get('http://my-app-heqr6b54qa-uc.a.run.app/api/getPostingAccount', {
                                                    params: {
                                                        trello_card_id: trelloCardId,
                                                        ignore_daily_limit: true
                                                    }
                                                });
                                                // If we got a valid response, use the returned posting account
                                                if (postingAccountResponse.data && postingAccountResponse.data.account) {
                                                    postingAccount = postingAccountResponse.data.account;
                                                    console.log(`Auto-assigned posting account: ${postingAccount}`);
                                                }
                                            } catch (autoAssignError) {
                                                console.error('Error auto-assigning posting account:', autoAssignError);
                                            // Continue with default 'clay' if auto-assign fails
                                            }
                                        }
                                        if (postingAccountField) {
                                            // Set Posting Account custom field with either the original account or the auto-assigned one
                                            await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].put(`https://api.trello.com/1/cards/${trelloCardId}/customField/${postingAccountField.id}/item`, {
                                                value: {
                                                    text: postingAccount
                                                }
                                            }, {
                                                params: {
                                                    key: apiKey,
                                                    token: apiToken
                                                }
                                            });
                                        }
                                        // Insert into ab_video table with project_id and variable_id
                                        await client.query(`INSERT INTO ab_video (trello_card_id, variable_id, project_id)
                       VALUES ($1, $2, $3)`, [
                                            trelloCardId,
                                            variableId,
                                            projectId
                                        ]);
                                        // Get the account_id for the potentially updated posting account
                                        if (postingAccount !== resource.account) {
                                            try {
                                                const updatedAccountResult = await client.query(`SELECT id FROM posting_accounts WHERE account = $1`, [
                                                    postingAccount
                                                ]);
                                                if (updatedAccountResult.rows.length > 0) {
                                                    accountId = updatedAccountResult.rows[0].id;
                                                }
                                            } catch (error) {
                                                console.error('Error getting updated account ID:', error);
                                            // Continue with previously determined account_id
                                            }
                                        }
                                        // Insert script into AB script table with writer_id=129
                                        const insertRes = await client.query(`INSERT INTO ab_script (
                         title,
                         google_doc_link,
                         approval_status,
                         created_at,
                         writer_id,
                         account_id
                       )
                       VALUES ($1, $2, $3, CURRENT_TIMESTAMP, $4, $5)
                       RETURNING *`, [
                                            cardTitle,
                                            resource.googleDocLink,
                                            "Approved Script. Ready for production",
                                            129,
                                            accountId || 1 // Use the account_id we got earlier or default to 1
                                        ]);
                                        const script = insertRes.rows[0];
                                        // Update script with Trello card ID
                                        await client.query("UPDATE ab_script SET trello_card_id = $1 WHERE id = $2", [
                                            trelloCardId,
                                            script.id
                                        ]);
                                    } catch (trelloError) {
                                        console.error('Error creating AB Trello card:', trelloError);
                                    // Continue even if Trello card creation fails
                                    }
                                } catch (trelloError) {
                                    console.error('Error creating AB Trello card:', trelloError);
                                // Continue even if Trello card creation fails
                                }
                            }
                        }
                    }
                }
            }
            // Commit the transaction
            await client.query('COMMIT');
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                projectId,
                message: 'AB Project created successfully'
            });
        } catch (dbError) {
            // Rollback in case of error
            await client.query('ROLLBACK');
            console.error('Database error:', dbError);
            throw dbError;
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error creating AB project:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to create AB project'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__4a3b8bcd._.js.map