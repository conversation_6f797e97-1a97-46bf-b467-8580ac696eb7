{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/lib/db.ts"], "sourcesContent": ["import { Pool, PoolConfig } from 'pg';\n\n// Parse the service account credentials from environment variable\nlet credentials: any = null;\ntry {\n  if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {\n    credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);\n  }\n} catch (error) {\n  console.error('Error parsing Google credentials:', error);\n}\n\n// Cloud SQL connection configuration\nconst getPoolConfig = (): PoolConfig => {\n  // Log environment variables for debugging\n  console.log('Environment variables:');\n  console.log('USE_CLOUD_SQL_AUTH_PROXY:', process.env.USE_CLOUD_SQL_AUTH_PROXY);\n  console.log('DB_HOST:', process.env.DB_HOST);\n  console.log('DB_PORT:', process.env.DB_PORT);\n  console.log('NODE_ENV:', process.env.NODE_ENV);\n\n  // Force direct connection regardless of environment variable\n  // This is a temporary fix to ensure we're using direct connection\n  const config = {\n    host: '***********', // Hardcoded for now\n    database: process.env.DB_NAME || 'postgres',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASS || 'Plotpointe!@3456',\n    port: 5432, // Hardcoded for now\n    ssl: { rejectUnauthorized: false } // Enable SSL with rejectUnauthorized: false\n  };\n\n  console.log('Using database config:', JSON.stringify(config, null, 2));\n  return config;\n};\n\n// Create the connection pool\nconst pool = new Pool(getPoolConfig());\n\n// Add error handler to log connection issues\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nexport default pool;\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,kEAAkE;AAClE,IAAI,cAAmB;AACvB,IAAI;IACF,IAAI,QAAQ,GAAG,CAAC,mCAAmC,EAAE;QACnD,cAAc,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,mCAAmC;IAC1E;AACF,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,qCAAqC;AACrD;AAEA,qCAAqC;AACrC,MAAM,gBAAgB;IACpB,0CAA0C;IAC1C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,GAAG,CAAC,wBAAwB;IAC7E,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC;IAEZ,6DAA6D;IAC7D,kEAAkE;IAClE,MAAM,SAAS;QACb,MAAM;QACN,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM;QACN,KAAK;YAAE,oBAAoB;QAAM,EAAE,4CAA4C;IACjF;IAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,QAAQ,MAAM;IACnE,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;AAEtB,6CAA6C;AAC7C,KAAK,EAAE,CAAC,SAAS,CAAC;IAChB,QAAQ,KAAK,CAAC,mCAAmC;IACjD,QAAQ,IAAI,CAAC,CAAC;AAChB;uCAEe", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport pool from '@/lib/db';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { username, password } = await request.json();\n\n    // Validate input\n    if (!username || !password) {\n      return NextResponse.json(\n        { error: 'Username and password are required' },\n        { status: 400 }\n      );\n    }\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Query the database for the user from the login table\n      const result = await client.query(\n        'SELECT * FROM login WHERE username = $1',\n        [username]\n      );\n\n      // Check if user exists\n      if (result.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Invalid login credentials' },\n          { status: 401 }\n        );\n      }\n\n      const user = result.rows[0];\n\n      // In a real app, you would use bcrypt to compare hashed passwords\n      // This is a simplified example\n      if (user.password !== password) {\n        return NextResponse.json(\n          { error: 'Invalid login credentials' },\n          { status: 401 }\n        );\n      }\n\n      // Return user data (excluding sensitive information)\n      const userData = {\n        id: user.id,\n        username: user.username,\n        // Add any other non-sensitive user data here\n      };\n\n      return NextResponse.json({ user: userData });\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { error: 'Authentication failed' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,iBAAiB;QACjB,IAAI,CAAC,YAAY,CAAC,UAAU;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqC,GAC9C;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,uDAAuD;YACvD,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,2CACA;gBAAC;aAAS;YAGZ,uBAAuB;YACvB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA4B,GACrC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,OAAO,OAAO,IAAI,CAAC,EAAE;YAE3B,kEAAkE;YAClE,+BAA+B;YAC/B,IAAI,KAAK,QAAQ,KAAK,UAAU;gBAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA4B,GACrC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,qDAAqD;YACrD,MAAM,WAAW;gBACf,IAAI,KAAK,EAAE;gBACX,UAAU,KAAK,QAAQ;YAEzB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,MAAM;YAAS;QAC5C,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}