{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/lib/db.ts"], "sourcesContent": ["import { Pool, PoolConfig } from 'pg';\n\n// Parse the service account credentials from environment variable\nlet credentials: any = null;\ntry {\n  if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {\n    credentials = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);\n  }\n} catch (error) {\n  console.error('Error parsing Google credentials:', error);\n}\n\n// Cloud SQL connection configuration\nconst getPoolConfig = (): PoolConfig => {\n  // Log environment variables for debugging\n  console.log('Environment variables:');\n  console.log('USE_CLOUD_SQL_AUTH_PROXY:', process.env.USE_CLOUD_SQL_AUTH_PROXY);\n  console.log('DB_HOST:', process.env.DB_HOST);\n  console.log('DB_PORT:', process.env.DB_PORT);\n  console.log('NODE_ENV:', process.env.NODE_ENV);\n\n  // Force direct connection regardless of environment variable\n  // This is a temporary fix to ensure we're using direct connection\n  const config = {\n    host: '***********', // Hardcoded for now\n    database: process.env.DB_NAME || 'postgres',\n    user: process.env.DB_USER || 'postgres',\n    password: process.env.DB_PASS || 'Plotpointe!@3456',\n    port: 5432, // Hardcoded for now\n    ssl: { rejectUnauthorized: false } // Enable SSL with rejectUnauthorized: false\n  };\n\n  console.log('Using database config:', JSON.stringify(config, null, 2));\n  return config;\n};\n\n// Create the connection pool\nconst pool = new Pool(getPoolConfig());\n\n// Add error handler to log connection issues\npool.on('error', (err) => {\n  console.error('Unexpected error on idle client', err);\n  process.exit(-1);\n});\n\nexport default pool;\n"], "names": [], "mappings": ";;;AAAA;;;;;;AAEA,kEAAkE;AAClE,IAAI,cAAmB;AACvB,IAAI;IACF,IAAI,QAAQ,GAAG,CAAC,mCAAmC,EAAE;QACnD,cAAc,KAAK,KAAK,CAAC,QAAQ,GAAG,CAAC,mCAAmC;IAC1E;AACF,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,qCAAqC;AACrD;AAEA,qCAAqC;AACrC,MAAM,gBAAgB;IACpB,0CAA0C;IAC1C,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,6BAA6B,QAAQ,GAAG,CAAC,wBAAwB;IAC7E,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;IAC3C,QAAQ,GAAG,CAAC;IAEZ,6DAA6D;IAC7D,kEAAkE;IAClE,MAAM,SAAS;QACb,MAAM;QACN,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM,QAAQ,GAAG,CAAC,OAAO,IAAI;QAC7B,UAAU,QAAQ,GAAG,CAAC,OAAO,IAAI;QACjC,MAAM;QACN,KAAK;YAAE,oBAAoB;QAAM,EAAE,4CAA4C;IACjF;IAEA,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,QAAQ,MAAM;IACnE,OAAO;AACT;AAEA,6BAA6B;AAC7B,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;AAEtB,6CAA6C;AAC7C,KAAK,EAAE,CAAC,SAAS,CAAC;IAChB,QAAQ,KAAK,CAAC,mCAAmC;IACjD,QAAQ,IAAI,CAAC,CAAC;AAChB;uCAEe", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/api/videos/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport pool from '@/lib/db';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const searchParams = request.nextUrl.searchParams;\n    const variableId = searchParams.get('variableId');\n\n    if (!variableId) {\n      return NextResponse.json(\n        { error: 'Variable ID is required' },\n        { status: 400 }\n      );\n    }\n\n    // Connect to PostgreSQL\n    const client = await pool.connect();\n\n    try {\n      // Query the database for YouTube URLs associated with this variable\n      const result = await client.query(\n        `SELECT id, url, created, writer_id, script_title, account_id, video_cat\n         FROM video\n         WHERE variable_id = $1\n         AND url IS NOT NULL\n         AND url != ''`,\n        [variableId]\n      );\n\n      // Process YouTube URLs\n      const videos = result.rows.map(row => {\n        return {\n          id: row.id,\n          url: row.url,\n          created: row.created,\n          writer_id: row.writer_id,\n          title: row.script_title || `YouTube Video ${row.id}`,\n          account_id: row.account_id,\n          video_cat: row.video_cat\n        };\n      }).filter(video => video.url && video.url.trim() !== '');\n\n      return NextResponse.json({ videos });\n    } finally {\n      client.release();\n    }\n  } catch (error) {\n    console.error('Error fetching videos:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch videos' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,eAAe,QAAQ,OAAO,CAAC,YAAY;QACjD,MAAM,aAAa,aAAa,GAAG,CAAC;QAEpC,IAAI,CAAC,YAAY;YACf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,MAAM,kHAAA,CAAA,UAAI,CAAC,OAAO;QAEjC,IAAI;YACF,oEAAoE;YACpE,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,CAAC;;;;sBAIa,CAAC,EACf;gBAAC;aAAW;YAGd,uBAAuB;YACvB,MAAM,SAAS,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC7B,OAAO;oBACL,IAAI,IAAI,EAAE;oBACV,KAAK,IAAI,GAAG;oBACZ,SAAS,IAAI,OAAO;oBACpB,WAAW,IAAI,SAAS;oBACxB,OAAO,IAAI,YAAY,IAAI,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE;oBACpD,YAAY,IAAI,UAAU;oBAC1B,WAAW,IAAI,SAAS;gBAC1B;YACF,GAAG,MAAM,CAAC,CAAA,QAAS,MAAM,GAAG,IAAI,MAAM,GAAG,CAAC,IAAI,OAAO;YAErD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE;YAAO;QACpC,SAAU;YACR,OAAO,OAAO;QAChB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}