{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/components/FloatingLogo.tsx"], "sourcesContent": ["'use client';\n\nimport { motion } from 'framer-motion';\nimport { FaChartLine } from 'react-icons/fa';\n\nexport default function FloatingLogo() {\n  return (\n    <motion.div\n      className=\"absolute top-10 left-1/2 transform -translate-x-1/2 z-10\"\n      animate={{\n        y: [0, 10, 0],\n        rotate: [0, 5, 0, -5, 0],\n      }}\n      transition={{\n        duration: 6,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }}\n    >\n      <div className=\"flex items-center justify-center w-20 h-20 bg-white rounded-full shadow-xl\">\n        <motion.div\n          animate={{\n            scale: [1, 1.1, 1],\n          }}\n          transition={{\n            duration: 3,\n            repeat: Infinity,\n            ease: \"easeInOut\"\n          }}\n        >\n          <FaChartLine className=\"text-4xl text-indigo-600\" />\n        </motion.div>\n      </div>\n      <motion.div\n        className=\"absolute -bottom-3 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-indigo-500 rounded-full opacity-30\"\n        animate={{\n          width: [16, 14, 16],\n          opacity: [0.3, 0.2, 0.3],\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: \"easeInOut\"\n        }}\n      />\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YACP,GAAG;gBAAC;gBAAG;gBAAI;aAAE;YACb,QAAQ;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;gBAAG;aAAE;QAC1B;QACA,YAAY;YACV,UAAU;YACV,QAAQ;YACR,MAAM;QACR;;0BAEA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;oBACpB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,MAAM;oBACR;8BAEA,cAAA,6LAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAG3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAI;wBAAI;qBAAG;oBACnB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;;;;;;;;;;;;AAIR;KA1CwB", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/components/AnimatedLoginForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { FaUser, FaLock, FaSignInAlt } from 'react-icons/fa';\n\nexport default function AnimatedLoginForm() {\n  const [username, setUsername] = useState('');\n  const [password, setPassword] = useState('');\n  const [error, setError] = useState('');\n  const [loading, setLoading] = useState(false);\n  const router = useRouter();\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      // For development/demo purposes, allow hardcoded logins\n      if ((username === 'admin' && password === 'password') ||\n          (username === 'clay' && password === '1234')) {\n        router.push('/dashboard');\n        return;\n      }\n\n      // Call the API route for authentication\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ username, password }),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Authentication failed');\n      }\n\n      // Store user data in localStorage or a state management solution\n      localStorage.setItem('user', JSON.stringify(data.user));\n\n      // Redirect to dashboard\n      router.push('/dashboard');\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1,\n        delayChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { \n      opacity: 1, \n      y: 0,\n      transition: { duration: 0.5 }\n    }\n  };\n\n  const buttonVariants = {\n    hidden: { opacity: 0, scale: 0.8 },\n    visible: { \n      opacity: 1, \n      scale: 1,\n      transition: { duration: 0.5 }\n    },\n    hover: { \n      scale: 1.05,\n      boxShadow: \"0px 5px 15px rgba(79, 70, 229, 0.4)\",\n      transition: { duration: 0.3 }\n    },\n    tap: { \n      scale: 0.95,\n      transition: { duration: 0.1 }\n    }\n  };\n\n  return (\n    <motion.div\n      className=\"w-full max-w-md bg-white rounded-xl shadow-2xl overflow-hidden backdrop-blur-sm bg-white/90\"\n      initial={{ opacity: 0, y: 50 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.7, ease: \"easeOut\" }}\n    >\n      <motion.div \n        className=\"bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-center relative overflow-hidden\"\n        initial={{ height: 0 }}\n        animate={{ height: \"auto\" }}\n        transition={{ duration: 0.5, delay: 0.2 }}\n      >\n        <motion.div\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5, delay: 0.5 }}\n        >\n          <h1 className=\"text-3xl font-bold text-white\">Project Tracker</h1>\n          <p className=\"text-blue-100 mt-2\">Sign in to your account</p>\n        </motion.div>\n        \n        {/* Animated background elements */}\n        <motion.div \n          className=\"absolute -top-10 -right-10 w-40 h-40 rounded-full bg-blue-500/20\"\n          animate={{ \n            scale: [1, 1.2, 1],\n            rotate: [0, 90, 0],\n          }}\n          transition={{ \n            duration: 20, \n            repeat: Infinity,\n            ease: \"easeInOut\" \n          }}\n        />\n        <motion.div \n          className=\"absolute -bottom-20 -left-10 w-60 h-60 rounded-full bg-indigo-500/20\"\n          animate={{ \n            scale: [1, 1.1, 1],\n            rotate: [0, -60, 0],\n          }}\n          transition={{ \n            duration: 25, \n            repeat: Infinity,\n            ease: \"easeInOut\" \n          }}\n        />\n      </motion.div>\n\n      <motion.form \n        onSubmit={handleLogin} \n        className=\"p-8 space-y-6\"\n        variants={formVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {error && (\n          <motion.div \n            className=\"bg-red-50 text-red-500 p-3 rounded-lg text-sm\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n          >\n            {error}\n          </motion.div>\n        )}\n\n        <motion.div className=\"space-y-2\" variants={itemVariants}>\n          <label htmlFor=\"username\" className=\"block text-sm font-medium text-gray-700\">\n            Username\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <FaUser className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <motion.input\n              id=\"username\"\n              name=\"username\"\n              type=\"text\"\n              required\n              value={username}\n              onChange={(e) => setUsername(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n              placeholder=\"Enter your username\"\n              whileFocus={{ boxShadow: \"0px 0px 0px 2px rgba(79, 70, 229, 0.2)\" }}\n            />\n          </div>\n        </motion.div>\n\n        <motion.div className=\"space-y-2\" variants={itemVariants}>\n          <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n            Password\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <FaLock className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <motion.input\n              id=\"password\"\n              name=\"password\"\n              type=\"password\"\n              required\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 text-black\"\n              placeholder=\"Enter your password\"\n              whileFocus={{ boxShadow: \"0px 0px 0px 2px rgba(79, 70, 229, 0.2)\" }}\n            />\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <motion.button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-white bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\"\n            variants={buttonVariants}\n            whileHover=\"hover\"\n            whileTap=\"tap\"\n          >\n            {loading ? (\n              <span className=\"flex items-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Processing...\n              </span>\n            ) : (\n              <span className=\"flex items-center\">\n                <FaSignInAlt className=\"mr-2\" /> Sign In\n              </span>\n            )}\n          </motion.button>\n        </motion.div>\n      </motion.form>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,wDAAwD;YACxD,IAAI,AAAC,aAAa,WAAW,aAAa,cACrC,aAAa,UAAU,aAAa,QAAS;gBAChD,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,wCAAwC;YACxC,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAU;gBAAS;YAC5C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,iEAAiE;YACjE,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,KAAK,IAAI;YAErD,wBAAwB;YACxB,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,MAAM,iBAAiB;QACrB,QAAQ;YAAE,SAAS;YAAG,OAAO;QAAI;QACjC,SAAS;YACP,SAAS;YACT,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,OAAO;YACL,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,KAAK;YACH,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;;0BAE7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,QAAQ;gBAAE;gBACrB,SAAS;oBAAE,QAAQ;gBAAO;gBAC1B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;;0CAExC,6LAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAI;6BAAE;wBACpB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBACrB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,UAAU;gBACV,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;oBAEP,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;kCAE7B;;;;;;kCAIL,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;wBAAY,UAAU;;0CAC1C,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA0C;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCACX,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAU;wCACV,aAAY;wCACZ,YAAY;4CAAE,WAAW;wCAAyC;;;;;;;;;;;;;;;;;;kCAKxE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,WAAU;wBAAY,UAAU;;0CAC1C,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA0C;;;;;;0CAG9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;wCACX,IAAG;wCACH,MAAK;wCACL,MAAK;wCACL,QAAQ;wCACR,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAU;wCACV,aAAY;wCACZ,YAAY;4CAAE,WAAW;wCAAyC;;;;;;;;;;;;;;;;;;kCAKxE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,MAAK;4BACL,UAAU;4BACV,WAAU;4BACV,UAAU;4BACV,YAAW;4BACX,UAAS;sCAER,wBACC,6LAAC;gCAAK,WAAU;;kDACd,6LAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,6LAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,6LAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;qDAIR,6LAAC;gCAAK,WAAU;;kDACd,6LAAC,iJAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAjOwB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport dynamic from 'next/dynamic';\nimport AnimatedBackground from '@/components/AnimatedBackground';\nimport Floating<PERSON>ogo from '@/components/FloatingLogo';\nimport AnimatedLoginForm from '@/components/AnimatedLoginForm';\n\n// Disable SSR for the AnimatedBackground component\nconst DynamicAnimatedBackground = dynamic(\n  () => import('@/components/AnimatedBackground'),\n  { ssr: false }\n);\n\nexport default function Login() {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center p-4 relative overflow-hidden\">\n      {/* Animated background */}\n      <DynamicAnimatedBackground />\n\n      {/* Gradient overlay */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/90 to-indigo-900/90 -z-10\" />\n\n      {/* Floating logo */}\n      <FloatingLogo />\n\n      {/* Login form */}\n      <AnimatedLoginForm />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;AALA;;;;;AAOA,mDAAmD;AACnD,MAAM,4BAA4B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACtC;;;;;;IACE,KAAK;;KAFH;AAKS,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;;;;0BAGD,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,qIAAA,CAAA,UAAY;;;;;0BAGb,6LAAC,0IAAA,CAAA,UAAiB;;;;;;;;;;;AAGxB;MAhBwB", "debugId": null}}]}