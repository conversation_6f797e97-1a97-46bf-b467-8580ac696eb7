{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/components/AnimatedSidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { motion } from 'framer-motion';\nimport { \n  FaHome, \n  FaClipboardList, \n  FaChartLine, \n  FaCog, \n  FaSignOutAlt,\n  FaBars,\n  FaTimes\n} from 'react-icons/fa';\n\ninterface NavItem {\n  name: string;\n  icon: React.ReactNode;\n  path: string;\n}\n\nexport default function AnimatedSidebar() {\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isCollapsed, setIsCollapsed] = useState(false);\n  const [hoveredItem, setHoveredItem] = useState<string | null>(null);\n\n  const navItems: NavItem[] = [\n    { name: 'Dashboard', icon: <FaHome className=\"text-xl\" />, path: '/dashboard' },\n    { name: 'Projects', icon: <FaClipboardList className=\"text-xl\" />, path: '/dashboard/projects' },\n    { name: 'Analytics', icon: <FaChartLine className=\"text-xl\" />, path: '/dashboard/analytics' },\n    { name: 'Settings', icon: <FaCog className=\"text-xl\" />, path: '/dashboard/settings' },\n  ];\n\n  const handleLogout = () => {\n    router.push('/');\n  };\n\n  const isActive = (path: string) => {\n    if (path === '/dashboard') {\n      return pathname === '/dashboard';\n    }\n    return pathname?.startsWith(path);\n  };\n\n  return (\n    <motion.div \n      className=\"h-screen bg-gradient-to-b from-blue-900 to-indigo-900 text-white flex flex-col relative\"\n      initial={{ width: 256 }}\n      animate={{ width: isCollapsed ? 80 : 256 }}\n      transition={{ duration: 0.3, ease: \"easeInOut\" }}\n    >\n      {/* Toggle button */}\n      <motion.button\n        className=\"absolute -right-4 top-6 bg-indigo-600 rounded-full p-2 shadow-lg z-10\"\n        onClick={() => setIsCollapsed(!isCollapsed)}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.95 }}\n      >\n        {isCollapsed ? <FaBars size={14} /> : <FaTimes size={14} />}\n      </motion.button>\n\n      {/* Logo */}\n      <div className=\"p-5 border-b border-indigo-800 flex items-center justify-center\">\n        <motion.div\n          animate={{ rotate: [0, 5, 0, -5, 0] }}\n          transition={{ duration: 6, repeat: Infinity }}\n          className=\"text-3xl text-indigo-300 mr-3\"\n        >\n          <FaChartLine />\n        </motion.div>\n        {!isCollapsed && (\n          <motion.h1 \n            className=\"text-2xl font-bold\"\n            initial={{ opacity: 0, x: -20 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            Project Tracker\n          </motion.h1>\n        )}\n      </div>\n      \n      {/* Navigation */}\n      <nav className=\"flex-1 p-4 overflow-y-auto\">\n        <ul className=\"space-y-2\">\n          {navItems.map((item) => (\n            <motion.li key={item.name}\n              onHoverStart={() => setHoveredItem(item.name)}\n              onHoverEnd={() => setHoveredItem(null)}\n              whileHover={{ x: 5 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <a \n                href={item.path}\n                className={`flex items-center p-3 rounded-xl transition-all relative overflow-hidden ${\n                  isActive(item.path) \n                    ? 'text-white' \n                    : 'text-indigo-200 hover:text-white'\n                }`}\n              >\n                {/* Background highlight */}\n                {isActive(item.path) && (\n                  <motion.div \n                    className=\"absolute inset-0 bg-indigo-600 rounded-xl -z-10\"\n                    layoutId=\"activeBackground\"\n                    initial={false}\n                    transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                  />\n                )}\n                \n                {/* Hover effect */}\n                {hoveredItem === item.name && !isActive(item.path) && (\n                  <motion.div \n                    className=\"absolute inset-0 bg-indigo-700/50 rounded-xl -z-10\"\n                    layoutId=\"hoverBackground\"\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    exit={{ opacity: 0 }}\n                  />\n                )}\n                \n                {/* Icon */}\n                <motion.div \n                  className=\"flex items-center justify-center\"\n                  whileHover={{ rotate: [0, -10, 10, -10, 0] }}\n                  transition={{ duration: 0.5 }}\n                >\n                  {item.icon}\n                </motion.div>\n                \n                {/* Text */}\n                {!isCollapsed && (\n                  <motion.span \n                    className=\"ml-3 font-medium\"\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ duration: 0.2 }}\n                  >\n                    {item.name}\n                  </motion.span>\n                )}\n              </a>\n            </motion.li>\n          ))}\n        </ul>\n      </nav>\n      \n      {/* Logout button */}\n      <div className=\"p-4 mt-auto border-t border-indigo-800\">\n        <motion.button \n          onClick={handleLogout}\n          className=\"flex items-center w-full p-3 text-indigo-200 hover:text-white rounded-xl hover:bg-indigo-700/50 transition-colors\"\n          whileHover={{ x: 5 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          <FaSignOutAlt className=\"text-xl\" />\n          {!isCollapsed && (\n            <motion.span \n              className=\"ml-3 font-medium\"\n              initial={{ opacity: 0, x: -10 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              Logout\n            </motion.span>\n          )}\n        </motion.button>\n      </div>\n      \n      {/* Decorative elements */}\n      <div className=\"absolute bottom-0 left-0 w-full overflow-hidden h-40 pointer-events-none\">\n        <motion.div \n          className=\"absolute bottom-0 left-0 w-full h-full bg-gradient-to-t from-indigo-900/50 to-transparent\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1 }}\n        />\n        <motion.div \n          className=\"absolute bottom-10 left-10 w-20 h-20 rounded-full bg-indigo-600/10\"\n          animate={{ \n            scale: [1, 1.2, 1],\n            x: [0, 10, 0],\n            y: [0, -10, 0],\n          }}\n          transition={{ \n            duration: 8, \n            repeat: Infinity,\n            ease: \"easeInOut\" \n          }}\n        />\n        <motion.div \n          className=\"absolute bottom-20 right-5 w-10 h-10 rounded-full bg-blue-500/10\"\n          animate={{ \n            scale: [1, 1.5, 1],\n            x: [0, -10, 0],\n            y: [0, -5, 0],\n          }}\n          transition={{ \n            duration: 6, \n            repeat: Infinity,\n            ease: \"easeInOut\",\n            delay: 1\n          }}\n        />\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAqBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,WAAsB;QAC1B;YAAE,MAAM;YAAa,oBAAM,8OAAC,8IAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAa;QAC9E;YAAE,MAAM;YAAY,oBAAM,8OAAC,8IAAA,CAAA,kBAAe;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAsB;QAC/F;YAAE,MAAM;YAAa,oBAAM,8OAAC,8IAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAuB;QAC7F;YAAE,MAAM;YAAY,oBAAM,8OAAC,8IAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YAAc,MAAM;QAAsB;KACtF;IAED,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,UAAU,WAAW;IAC9B;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,OAAO;QAAI;QACtB,SAAS;YAAE,OAAO,cAAc,KAAK;QAAI;QACzC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAY;;0BAG/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS,IAAM,eAAe,CAAC;gBAC/B,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAK;0BAEvB,4BAAc,8OAAC,8IAAA,CAAA,SAAM;oBAAC,MAAM;;;;;yCAAS,8OAAC,8IAAA,CAAA,UAAO;oBAAC,MAAM;;;;;;;;;;;0BAIvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ;gCAAC;gCAAG;gCAAG;gCAAG,CAAC;gCAAG;6BAAE;wBAAC;wBACpC,YAAY;4BAAE,UAAU;4BAAG,QAAQ;wBAAS;wBAC5C,WAAU;kCAEV,cAAA,8OAAC,8IAAA,CAAA,cAAW;;;;;;;;;;oBAEb,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC9B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;kCAC7B;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BACX,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,cAAc,IAAM,eAAe,KAAK,IAAI;4BAC5C,YAAY,IAAM,eAAe;4BACjC,YAAY;gCAAE,GAAG;4BAAE;4BACnB,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,8OAAC;gCACC,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC,yEAAyE,EACnF,SAAS,KAAK,IAAI,IACd,eACA,oCACJ;;oCAGD,SAAS,KAAK,IAAI,mBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAS;wCACT,SAAS;wCACT,YAAY;4CAAE,MAAM;4CAAU,WAAW;4CAAK,SAAS;wCAAG;;;;;;oCAK7D,gBAAgB,KAAK,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,mBAC/C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,UAAS;wCACT,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,MAAM;4CAAE,SAAS;wCAAE;;;;;;kDAKvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,QAAQ;gDAAC;gDAAG,CAAC;gDAAI;gDAAI,CAAC;gDAAI;6CAAE;wCAAC;wCAC3C,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,KAAK,IAAI;;;;;;oCAIX,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,KAAK,IAAI;;;;;;;;;;;;2BApDF,KAAK,IAAI;;;;;;;;;;;;;;;0BA8D/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;oBACT,WAAU;oBACV,YAAY;wBAAE,GAAG;oBAAE;oBACnB,UAAU;wBAAE,OAAO;oBAAK;;sCAExB,8OAAC,8IAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBACvB,CAAC,6BACA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4BACV,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC9B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;4BAAI;sCAC7B;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;wBAAE;;;;;;kCAE5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,GAAG;gCAAC;gCAAG;gCAAI;6BAAE;4BACb,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;wBAChB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,GAAG;gCAAC;gCAAG,CAAC;gCAAI;6BAAE;4BACd,GAAG;gCAAC;gCAAG,CAAC;gCAAG;6BAAE;wBACf;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;4BACN,OAAO;wBACT;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 452, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/app/dashboard/layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { format } from 'date-fns';\nimport { motion } from 'framer-motion';\nimport { FaUser } from 'react-icons/fa';\nimport AnimatedSidebar from '@/components/AnimatedSidebar';\n\nexport default function DashboardLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  const router = useRouter();\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [username, setUsername] = useState('admin'); // In a real app, get from auth\n\n  // Update time every minute\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 60000);\n\n    return () => clearInterval(timer);\n  }, []);\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 overflow-hidden\">\n      {/* Animated Sidebar */}\n      <AnimatedSidebar />\n\n      {/* Main Content */}\n      <motion.div\n        className=\"flex-1 flex flex-col overflow-hidden\"\n        initial={{ opacity: 0, x: 20 }}\n        animate={{ opacity: 1, x: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        {/* Header */}\n        <motion.header\n          className=\"bg-white shadow-md\"\n          initial={{ y: -50, opacity: 0 }}\n          animate={{ y: 0, opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        >\n          <div className=\"flex justify-between items-center p-4\">\n            <motion.h2\n              className=\"text-xl font-semibold text-gray-800\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.5, delay: 0.4 }}\n            >\n              Dashboard\n            </motion.h2>\n\n            <div className=\"flex items-center space-x-6\">\n              <motion.div\n                className=\"text-gray-600 bg-gray-50 py-1 px-3 rounded-full shadow-sm\"\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.5 }}\n              >\n                {format(currentTime, 'MMMM d, yyyy • h:mm a')}\n              </motion.div>\n\n              <motion.div\n                className=\"flex items-center space-x-2 bg-indigo-50 py-1 px-3 rounded-full shadow-sm\"\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: 0.6 }}\n                whileHover={{ scale: 1.05 }}\n              >\n                <div className=\"bg-indigo-600 text-white p-2 rounded-full\">\n                  <FaUser />\n                </div>\n                <span className=\"font-medium text-indigo-800\">{username}</span>\n              </motion.div>\n            </div>\n          </div>\n        </motion.header>\n\n        {/* Page Content */}\n        <motion.main\n          className=\"flex-1 overflow-y-auto p-6 bg-gray-100\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5, delay: 0.3 }}\n        >\n          {children}\n        </motion.main>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS,gBAAgB,EACtC,QAAQ,EAGT;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,+BAA+B;IAElF,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAGhB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,SAAS;4BAAE,GAAG,CAAC;4BAAI,SAAS;wBAAE;wBAC9B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAExC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CACzC;;;;;;8CAID,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;sDAEvC,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,aAAa;;;;;;sDAGvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAI;4CAClC,SAAS;gDAAE,SAAS;gDAAG,OAAO;4CAAE;4CAChC,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;4CACxC,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,SAAM;;;;;;;;;;8DAET,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvD,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,UAAU;4BAAK,OAAO;wBAAI;kCAEvC;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}