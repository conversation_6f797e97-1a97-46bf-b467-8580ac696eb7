"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  FaPlus,
  FaFolder,
  FaFolderOpen,
  FaCalendarAlt,
  FaUsers,
  FaTasks,
  FaTrash,
  FaLink,
  FaYoutube,
  FaFileAlt,
  FaUserAlt,
  FaEdit,
  FaPencilAlt
} from 'react-icons/fa';

export default function Dashboard() {
  const router = useRouter();
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [projects, setProjects] = useState<Array<{
    id: number;
    name: string;
    tasks: number;
    members: number;
    dueDate: string;
  }>>([]);

  const [accounts, setAccounts] = useState<{id: number, account: string}[]>([]);
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    variables: [
      {
        name: '',
        note: '',
        resources: [
          {
            googleDocLink: '',
            note: '',
            youtubeUrl: '',
            account: 'Auto Assign'  // Set default to Auto Assign
          }
        ]
      }
    ]
  });

  // Load projects from the database
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        // Try to fetch from the API
        const response = await fetch('/api/projects');

        if (response.ok) {
          const data = await response.json();

          // Transform the data to match our expected format
          const formattedProjects = data.projects.map((p: any) => ({
            id: p.id,
            name: p.name,
            tasks: parseInt(p.variable_count) || 0,
            members: p.members || 1,
            dueDate: new Date(p.created_at).toISOString().split('T')[0]
          }));

          setProjects(formattedProjects);
        } else {
          // If API fails, try to load from localStorage as fallback
          const savedProjects = localStorage.getItem('projects');
          if (savedProjects) {
            try {
              setProjects(JSON.parse(savedProjects));
            } catch (error) {
              console.error('Error parsing saved projects:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching projects:', error);

        // Fallback to localStorage
        const savedProjects = localStorage.getItem('projects');
        if (savedProjects) {
          try {
            setProjects(JSON.parse(savedProjects));
          } catch (error) {
            console.error('Error parsing saved projects:', error);
          }
        }
      }
    };

    fetchProjects();
  }, []);

  // Fetch accounts from the API
  useEffect(() => {
    const fetchAccounts = async () => {
      try {
        // First try the regular accounts endpoint
        try {
          console.log('Trying to fetch accounts from /api/accounts...');
          const response = await fetch('/api/accounts');

          if (!response.ok) {
            throw new Error('Failed to fetch accounts from regular endpoint');
          }

          const data = await response.json();
          console.log('Successfully fetched accounts from /api/accounts:', data);
          // Add Auto Assign to the accounts array
          setAccounts([{ id: 0, account: 'Auto Assign' }, ...data.accounts]);
          return; // Exit if successful
        } catch (apiError) {
          console.error('Regular API error:', apiError);
          // If regular endpoint fails, try the direct endpoint
        }

        // Try the direct accounts endpoint
        try {
          console.log('Trying to fetch accounts from /api/direct-accounts...');
          const directResponse = await fetch('/api/direct-accounts');

          if (!directResponse.ok) {
            throw new Error('Failed to fetch accounts from direct endpoint');
          }

          const directData = await directResponse.json();
          console.log('Successfully fetched accounts from /api/direct-accounts:', directData);
          // Add Auto Assign to the accounts array
          setAccounts([{ id: 0, account: 'Auto Assign' }, ...directData.accounts]);
          return; // Exit if successful
        } catch (directApiError) {
          console.error('Direct API error:', directApiError);
          // If direct endpoint fails, try the debug endpoint
        }

        // Try the debug endpoint to get more information
        try {
          console.log('Trying to fetch debug information...');
          const debugResponse = await fetch('/api/debug');

          if (debugResponse.ok) {
            const debugData = await debugResponse.json();
            console.log('Debug information:', debugData);
          }
        } catch (debugError) {
          console.error('Debug API error:', debugError);
        }

        // Fallback to mock data as last resort
        console.log('Falling back to mock data');
        setAccounts([
          { id: 0, account: 'Auto Assign' },
          { id: 1, account: 'account1' },
          { id: 2, account: 'account2' },
          { id: 3, account: 'account3' }
        ]);
      } catch (error) {
        console.error('Error in account fetching process:', error);
      }
    };

    fetchAccounts();
  }, []);

  // Add a new variable to the project
  const addVariable = () => {
    setNewProject({
      ...newProject,
      variables: [
        ...newProject.variables,
        {
          name: '',
          note: '',
          resources: [
            {
              googleDocLink: '',
              note: '',
              youtubeUrl: ''
            }
          ]
        }
      ]
    });
  };

  // Add a new resource to a variable
  const addResource = (variableIndex: number) => {
    const updatedVariables = [...newProject.variables];
    updatedVariables[variableIndex].resources.push({
      googleDocLink: '',
      note: '',
      youtubeUrl: '',
      account: 'Auto Assign'  // Set default to Auto Assign
    });

    setNewProject({
      ...newProject,
      variables: updatedVariables
    });
  };

  // Remove a variable
  const removeVariable = (index: number) => {
    const updatedVariables = newProject.variables.filter((_, i) => i !== index);
    setNewProject({
      ...newProject,
      variables: updatedVariables
    });
  };

  // Remove a resource
  const removeResource = (variableIndex: number, resourceIndex: number) => {
    const updatedVariables = [...newProject.variables];
    updatedVariables[variableIndex].resources = updatedVariables[variableIndex].resources.filter((_, i) => i !== resourceIndex);

    setNewProject({
      ...newProject,
      variables: updatedVariables
    });
  };

  // Update variable field
  const updateVariableField = (variableIndex: number, field: string, value: string) => {
    const updatedVariables = [...newProject.variables];
    updatedVariables[variableIndex] = {
      ...updatedVariables[variableIndex],
      [field]: value
    };

    setNewProject({
      ...newProject,
      variables: updatedVariables
    });
  };

  // Update resource field
  const updateResourceField = (variableIndex: number, resourceIndex: number, field: string, value: string) => {
    const updatedVariables = [...newProject.variables];
    updatedVariables[variableIndex].resources[resourceIndex] = {
      ...updatedVariables[variableIndex].resources[resourceIndex],
      [field]: value
    };

    setNewProject({
      ...newProject,
      variables: updatedVariables
    });
  };

  const [createProjectLoading, setCreateProjectLoading] = useState(false);
  const [createProjectError, setCreateProjectError] = useState('');

  // Edit project state
  const [showEditProjectModal, setShowEditProjectModal] = useState(false);
  const [editProject, setEditProject] = useState<any>(null);
  const [editProjectLoading, setEditProjectLoading] = useState(false);
  const [editProjectError, setEditProjectError] = useState('');

  // Function to clear all projects (for testing)
  const clearAllProjects = () => {
    setProjects([]);
    localStorage.removeItem('projects');
  };

  // Function to handle opening the edit modal
  const handleEditProject = async (e: React.MouseEvent, projectId: number) => {
    e.stopPropagation(); // Prevent navigation to project detail page

    try {
      // Fetch the full project data from the API
      const response = await fetch(`/api/projects/${projectId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch project details');
      }

      const data = await response.json();
      const project = data.project;

      // Debug: Log the raw project data to check YouTube URLs
      console.log('Raw project data:', project);

      // Check if variables and resources exist
      if (project.variables) {
        project.variables.forEach((variable: any, vIndex: number) => {
          console.log(`Variable ${vIndex + 1} (${variable.name}):`);
          if (variable.resources) {
            variable.resources.forEach((resource: any, rIndex: number) => {
              console.log(`  Resource ${rIndex + 1} - YouTube URL:`, resource.youtube_url);
            });
          }
        });
      }

      // Format the project data for the edit form
      const formattedProject = {
        id: project.id,
        name: project.name,
        description: project.description || '',
        members: project.members || 1,
        variables: project.variables.map((variable: any) => ({
          id: variable.id,
          name: variable.name,
          note: variable.note || '',
          resources: variable.resources.map((resource: any) => {
            // Debug log for each resource
            console.log(`Processing resource:`, {
              id: resource.id,
              google_doc_link: resource.google_doc_link,
              youtube_url: resource.youtube_url,
              youtube_url_from_note: resource.youtube_url_from_note,
              from_video_table: resource.from_video_table,
              video_id: resource.video_id
            });

            return {
              id: resource.id,
              googleDocLink: resource.google_doc_link || '',
              youtubeUrl: resource.youtube_url || '',
              note: resource.note || '',
              account: resource.account || '',
              // Preserve special flags for resources from the video table
              from_video_table: resource.from_video_table || false,
              video_id: resource.video_id || null,
              trello_card_id: resource.trello_card_id || null
            };
          })
        })),
        deletedVariableIds: [],
        deletedResourceIds: []
      };

      // Debug: Log the formatted project data to verify YouTube URLs are mapped correctly
      console.log('Formatted project data:', formattedProject);

      // Debug: Check specifically for YouTube URLs in the formatted data
      if (formattedProject.variables) {
        formattedProject.variables.forEach((variable: any, vIndex: number) => {
          console.log(`Formatted Variable ${vIndex + 1} (${variable.name}):`);
          if (variable.resources) {
            variable.resources.forEach((resource: any, rIndex: number) => {
              console.log(`  Resource ${rIndex + 1} - YouTube URL:`, resource.youtubeUrl);
            });
          }
        });
      }

      setEditProject(formattedProject);
      setShowEditProjectModal(true);
    } catch (error) {
      console.error('Error fetching project for editing:', error);
      alert('Failed to load project for editing. Please try again.');
    }
  };

  // Add a new variable to the edit project
  const addEditVariable = () => {
    if (!editProject) return;

    setEditProject({
      ...editProject,
      variables: [
        ...editProject.variables,
        {
          name: '',
          note: '',
          resources: [
            {
              googleDocLink: '',
              note: '',
              youtubeUrl: ''
            }
          ]
        }
      ]
    });
  };

  // Add a new resource to a variable in edit project
  const addEditResource = (variableIndex: number) => {
    if (!editProject) return;

    const updatedVariables = [...editProject.variables];
    updatedVariables[variableIndex].resources.push({
      googleDocLink: '',
      note: '',
      youtubeUrl: '',
      account: ''
    });

    setEditProject({
      ...editProject,
      variables: updatedVariables
    });
  };

  // Remove a variable from edit project
  const removeEditVariable = (index: number) => {
    if (!editProject) return;

    const variable = editProject.variables[index];
    const updatedVariables = editProject.variables.filter((_: any, i: number) => i !== index);

    // If the variable has an ID, add it to deletedVariableIds
    const updatedDeletedVariableIds = [...editProject.deletedVariableIds];
    if (variable.id) {
      updatedDeletedVariableIds.push(variable.id);
    }

    setEditProject({
      ...editProject,
      variables: updatedVariables,
      deletedVariableIds: updatedDeletedVariableIds
    });
  };

  // Remove a resource from edit project
  const removeEditResource = (variableIndex: number, resourceIndex: number) => {
    if (!editProject) return;

    const updatedVariables = [...editProject.variables];
    const resource = updatedVariables[variableIndex].resources[resourceIndex];

    updatedVariables[variableIndex].resources = updatedVariables[variableIndex].resources.filter((_: any, i: number) => i !== resourceIndex);

    // If the resource has an ID, track it for deletion
    let updatedDeletedResourceIds = [...(editProject.variables[variableIndex].deletedResourceIds || [])];
    if (resource.id) {
      if (!updatedDeletedResourceIds) {
        updatedDeletedResourceIds = [];
      }
      updatedDeletedResourceIds.push(resource.id);
    }

    // Update the variable with the new deletedResourceIds
    updatedVariables[variableIndex] = {
      ...updatedVariables[variableIndex],
      deletedResourceIds: updatedDeletedResourceIds
    };

    setEditProject({
      ...editProject,
      variables: updatedVariables
    });
  };

  // Update variable field in edit project
  const updateEditVariableField = (variableIndex: number, field: string, value: string) => {
    if (!editProject) return;

    const updatedVariables = [...editProject.variables];
    updatedVariables[variableIndex] = {
      ...updatedVariables[variableIndex],
      [field]: value
    };

    setEditProject({
      ...editProject,
      variables: updatedVariables
    });
  };

  // Update resource field in edit project
  const updateEditResourceField = (variableIndex: number, resourceIndex: number, field: string, value: string) => {
    if (!editProject) return;

    // Debug: Log the current value and the new value
    console.log(`Updating ${field} for variable ${variableIndex}, resource ${resourceIndex}`);
    console.log(`  Current value:`, editProject.variables[variableIndex].resources[resourceIndex][field]);
    console.log(`  New value:`, value);

    const updatedVariables = [...editProject.variables];
    updatedVariables[variableIndex].resources[resourceIndex] = {
      ...updatedVariables[variableIndex].resources[resourceIndex],
      [field]: value
    };

    // Debug: Log the updated resource
    console.log(`  Updated resource:`, updatedVariables[variableIndex].resources[resourceIndex]);

    setEditProject({
      ...editProject,
      variables: updatedVariables
    });
  };

  // Handle saving the edited project
  const handleUpdateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editProject) return;

    setEditProjectLoading(true);
    setEditProjectError('');

    try {
      // Call the API to update the project
      const response = await fetch(`/api/projects/${editProject.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editProject),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update project');
      }

      // Refresh the projects list
      const projectsResponse = await fetch('/api/projects');
      if (projectsResponse.ok) {
        const data = await projectsResponse.json();

        // Transform the data to match our expected format
        const formattedProjects = data.projects.map((p: any) => ({
          id: p.id,
          name: p.name,
          tasks: parseInt(p.variable_count) || 0,
          members: p.members || 1,
          dueDate: new Date(p.created_at).toISOString().split('T')[0]
        }));

        setProjects(formattedProjects);
      }

      // Close the modal
      setShowEditProjectModal(false);
      setEditProject(null);
    } catch (error: any) {
      setEditProjectError(error.message);
    } finally {
      setEditProjectLoading(false);
    }
  };

  const handleCreateProject = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreateProjectLoading(true);
    setCreateProjectError('');

    try {
      // For development/demo purposes, just add to local state if API fails
      try {
        // Call the API to save the project
        const response = await fetch('/api/ab-projects', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(newProject),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create project');
        }

        const data = await response.json();

        // Get current date in YYYY-MM-DD format
        const currentDate = new Date().toISOString().split('T')[0];

        // Create the new project object
        const newProjectObj = {
          id: data.projectId,
          name: newProject.name,
          tasks: newProject.variables.length,
          members: 1,
          dueDate: currentDate,
          variables: newProject.variables // Save the actual variables
        };

        // Add to local state
        const updatedProjects = [...projects, newProjectObj];
        setProjects(updatedProjects);

        // Save to localStorage
        localStorage.setItem('projects', JSON.stringify(updatedProjects));
      } catch (apiError: any) {
        console.error('API error:', apiError);
        // Fallback to local state only
        const newId = projects.length > 0 ? Math.max(...projects.map(p => p.id)) + 1 : 1;

        // Get current date in YYYY-MM-DD format
        const currentDate = new Date().toISOString().split('T')[0];

        // Create the new project object
        const newProjectObj = {
          id: newId,
          name: newProject.name,
          tasks: newProject.variables.length,
          members: 1,
          dueDate: currentDate,
          variables: newProject.variables // Save the actual variables
        };

        // Add to local state
        const updatedProjects = [...projects, newProjectObj];
        setProjects(updatedProjects);

        // Save to localStorage
        localStorage.setItem('projects', JSON.stringify(updatedProjects));
      }

      // Reset form
      setNewProject({
        name: '',
        description: '',
        variables: [
          {
            name: '',
            note: '',
            resources: [
              {
                googleDocLink: '',
                note: '',
                youtubeUrl: '',
                account: ''
              }
            ]
          }
        ]
      });

      setShowNewProjectModal(false);
    } catch (error: any) {
      setCreateProjectError(error.message);
    } finally {
      setCreateProjectLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <h1 className="text-2xl font-bold text-gray-800">Welcome to your Project Dashboard</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Create New Project Block */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
          <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4">
            <h2 className="text-xl font-semibold text-white">Create New Project</h2>
          </div>

          <div className="p-6">
            <p className="text-gray-600 mb-6">
              Start a new project and track your progress from start to finish.
            </p>

            <button
              onClick={() => setShowNewProjectModal(true)}
              className="w-full flex items-center justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
            >
              <FaPlus className="mr-2" /> Create New Project
            </button>
          </div>
        </div>

        {/* Existing Projects Block */}
        <div className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
          <div className="bg-gradient-to-r from-green-500 to-teal-600 p-4">
            <h2 className="text-xl font-semibold text-white">Your Projects</h2>
          </div>

          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <p className="text-gray-600">
                Select an existing project to view or edit.
              </p>
              {projects.length > 0 && (
                <button
                  onClick={clearAllProjects}
                  className="text-xs text-red-500 hover:text-red-700"
                  type="button"
                >
                  Clear All
                </button>
              )}
            </div>

            <div className="space-y-3 max-h-64 overflow-y-auto">
              {projects.length > 0 ? (
                projects.map(project => (
                  <div
                    key={project.id}
                    className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
                    onClick={() => router.push(`/dashboard/projects/${project.id}`)}
                  >
                    <div className="text-blue-500 mr-3">
                      <FaFolder />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{project.name}</h3>
                      <div className="flex items-center text-sm text-gray-500 mt-1 space-x-4">
                        <span className="flex items-center">
                          <FaTasks className="mr-1" /> {project.tasks} variables
                        </span>
                        <span className="flex items-center">
                          <FaUsers className="mr-1" /> {project.members} members
                        </span>
                        <span className="flex items-center">
                          <FaCalendarAlt className="mr-1" /> {project.dueDate}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => handleEditProject(e, project.id)}
                        className="text-gray-400 hover:text-yellow-500 transition-colors"
                        title="Edit Project"
                      >
                        <FaPencilAlt />
                      </button>
                      <div className="text-gray-400 hover:text-blue-500">
                        <FaFolderOpen />
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-gray-500">
                  No projects yet. Create your first project!
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* New Project Modal */}
      {showNewProjectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl my-8">
            <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-4 rounded-t-xl">
              <h2 className="text-xl font-semibold text-white">Create New Project</h2>
            </div>

            <form onSubmit={handleCreateProject} className="p-6 space-y-6 max-h-[80vh] overflow-y-auto">
              {createProjectError && (
                <div className="bg-red-50 text-red-500 p-3 rounded-lg text-sm mb-4">
                  {createProjectError}
                </div>
              )}

              {/* Basic Project Information */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <input
                  type="text"
                  id="name"
                  value={newProject.name}
                  onChange={(e) => setNewProject({...newProject, name: e.target.value})}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black"
                  required
                />
              </div>

              <div className="text-sm text-gray-500">
                <span>Creation Date: {new Date().toLocaleDateString()} {new Date().toLocaleTimeString()}</span>
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  value={newProject.description}
                  onChange={(e) => setNewProject({...newProject, description: e.target.value})}
                  rows={2}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black"
                />
              </div>



              {/* Variables Section */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Project Variables</h3>

                {newProject.variables.map((variable, variableIndex) => (
                  <div key={variableIndex} className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-medium text-gray-800">Variable {variableIndex + 1}</h4>
                      {variableIndex > 0 && (
                        <button
                          type="button"
                          onClick={() => removeVariable(variableIndex)}
                          className="text-red-500 hover:text-red-700"
                        >
                          <FaTrash />
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Variable Name
                        </label>
                        <input
                          type="text"
                          value={variable.name}
                          onChange={(e) => updateVariableField(variableIndex, 'name', e.target.value)}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black"
                          placeholder="Enter variable name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Variable Note
                        </label>
                        <input
                          type="text"
                          value={variable.note}
                          onChange={(e) => updateVariableField(variableIndex, 'note', e.target.value)}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-black"
                          placeholder="Add a note about this variable"
                        />
                      </div>
                    </div>

                    {/* Resources Section */}
                    <div className="mt-4">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Resources</h5>

                      {variable.resources.map((resource, resourceIndex) => (
                        <div key={resourceIndex} className="p-3 border border-gray-200 rounded-md mb-3 bg-white">
                          <div className="flex justify-between items-center mb-2">
                            <h6 className="text-sm font-medium text-gray-700">Resource {resourceIndex + 1}</h6>
                            <button
                              type="button"
                              onClick={() => removeResource(variableIndex, resourceIndex)}
                              className="text-red-500 hover:text-red-700 text-sm"
                            >
                              <FaTrash />
                            </button>
                          </div>

                          <div className="space-y-3">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaLink className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="url"
                                value={resource.googleDocLink}
                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'googleDocLink', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black"
                                placeholder="Google Doc Link"
                              />
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaFileAlt className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="text"
                                value={resource.note}
                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'note', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black"
                                placeholder="Resource Note"
                              />
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaYoutube className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="url"
                                value={resource.youtubeUrl}
                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'youtubeUrl', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black"
                                placeholder="YouTube URL"
                              />
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaUserAlt className="h-4 w-4 text-gray-400" />
                              </div>
                              <select
                                value={resource.account}
                                onChange={(e) => updateResourceField(variableIndex, resourceIndex, 'account', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 text-sm text-black"
                              >
                                {accounts.map(account => (
                                  <option key={account.id} value={account.account}>{account.account}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => addResource(variableIndex)}
                        className="mt-2 flex items-center text-sm text-indigo-600 hover:text-indigo-800"
                      >
                        <FaPlus className="mr-1" /> Add Another Resource
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addVariable}
                  className="mt-2 flex items-center text-indigo-600 hover:text-indigo-800"
                >
                  <FaPlus className="mr-1" /> Add Another Variable
                </button>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowNewProjectModal(false)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={createProjectLoading}
                  className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center justify-center min-w-[120px]"
                >
                  {createProjectLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Create Project'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Project Modal */}
      {showEditProjectModal && editProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
          <div className="bg-white rounded-xl shadow-xl w-full max-w-4xl my-8">
            <div className="bg-gradient-to-r from-yellow-500 to-amber-600 p-4 rounded-t-xl">
              <h2 className="text-xl font-semibold text-white">Edit Project: {editProject.name}</h2>
            </div>

            <form onSubmit={handleUpdateProject} className="p-6 space-y-6 max-h-[80vh] overflow-y-auto">
              {editProjectError && (
                <div className="bg-red-50 text-red-500 p-3 rounded-lg text-sm mb-4">
                  {editProjectError}
                </div>
              )}

              {/* Basic Project Information */}
              <div>
                <label htmlFor="edit-name" className="block text-sm font-medium text-gray-700 mb-1">
                  Project Name
                </label>
                <input
                  type="text"
                  id="edit-name"
                  value={editProject.name}
                  onChange={(e) => setEditProject({...editProject, name: e.target.value})}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black"
                  required
                />
              </div>

              <div>
                <label htmlFor="edit-description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="edit-description"
                  value={editProject.description}
                  onChange={(e) => setEditProject({...editProject, description: e.target.value})}
                  rows={2}
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black"
                />
              </div>



              {/* Variables Section */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Project Variables</h3>

                {editProject.variables.map((variable: any, variableIndex: number) => (
                  <div key={variableIndex} className="mb-6 p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="flex justify-between items-center mb-3">
                      <h4 className="text-md font-medium text-gray-800">Variable {variableIndex + 1}</h4>
                      <button
                        type="button"
                        onClick={() => removeEditVariable(variableIndex)}
                        className="text-red-500 hover:text-red-700"
                      >
                        <FaTrash />
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Variable Name
                        </label>
                        <input
                          type="text"
                          value={variable.name}
                          onChange={(e) => updateEditVariableField(variableIndex, 'name', e.target.value)}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black"
                          placeholder="Enter variable name"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Variable Note
                        </label>
                        <input
                          type="text"
                          value={variable.note}
                          onChange={(e) => updateEditVariableField(variableIndex, 'note', e.target.value)}
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-black"
                          placeholder="Add a note about this variable"
                        />
                      </div>
                    </div>

                    {/* Resources Section */}
                    <div className="mt-4">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">Resources</h5>

                      {variable.resources.map((resource: any, resourceIndex: number) => (
                        <div key={resourceIndex} className="p-3 border border-gray-200 rounded-md mb-3 bg-white">
                          <div className="flex justify-between items-center mb-2">
                            <h6 className="text-sm font-medium text-gray-700">Resource {resourceIndex + 1}</h6>
                            <button
                              type="button"
                              onClick={() => removeEditResource(variableIndex, resourceIndex)}
                              className="text-red-500 hover:text-red-700 text-sm"
                            >
                              <FaTrash />
                            </button>
                          </div>

                          <div className="space-y-3">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaLink className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="url"
                                value={resource.googleDocLink}
                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'googleDocLink', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black"
                                placeholder="Google Doc Link"
                              />
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaFileAlt className="h-4 w-4 text-gray-400" />
                              </div>
                              <input
                                type="text"
                                value={resource.note}
                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'note', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black"
                                placeholder="Resource Note"
                              />
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaYoutube className="h-4 w-4 text-gray-400" />
                              </div>
                              {/* Add a data attribute to help debug the value */}
                              <input
                                type="url"
                                value={resource.youtubeUrl || ''}
                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'youtubeUrl', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black"
                                placeholder="YouTube URL"
                                data-youtube-url={resource.youtubeUrl || ''}
                                data-resource-id={resource.id || ''}
                              />
                              {/* Add a debug element to show the value */}
                              {process.env.NODE_ENV === 'development' && (
                                <div className="text-xs text-gray-500 mt-1">
                                  Debug - YouTube URL: {resource.youtubeUrl || 'empty'}
                                </div>
                              )}
                            </div>

                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <FaUserAlt className="h-4 w-4 text-gray-400" />
                              </div>
                              <select
                                value={resource.account}
                                onChange={(e) => updateEditResourceField(variableIndex, resourceIndex, 'account', e.target.value)}
                                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-amber-500 focus:border-amber-500 text-sm text-black"
                              >
                                <option value="Auto Assign">Auto Assign</option>
                                {accounts.filter(account => account.account !== 'Auto Assign').map(account => (
                                  <option key={account.id} value={account.account}>{account.account}</option>
                                ))}
                              </select>
                            </div>
                          </div>
                        </div>
                      ))}

                      <button
                        type="button"
                        onClick={() => addEditResource(variableIndex)}
                        className="mt-2 flex items-center text-sm text-amber-600 hover:text-amber-800"
                      >
                        <FaPlus className="mr-1" /> Add Another Resource
                      </button>
                    </div>
                  </div>
                ))}

                <button
                  type="button"
                  onClick={addEditVariable}
                  className="mt-2 flex items-center text-amber-600 hover:text-amber-800"
                >
                  <FaPlus className="mr-1" /> Add Another Variable
                </button>
              </div>

              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => {
                    setShowEditProjectModal(false);
                    setEditProject(null);
                  }}
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={editProjectLoading}
                  className="px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 flex items-center justify-center min-w-[120px]"
                >
                  {editProjectLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    'Save Changes'
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}



