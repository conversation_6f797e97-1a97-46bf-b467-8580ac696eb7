{"name": "project-tracker-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "proxy": "node scripts/start-cloud-sql-proxy.js", "dev:proxy": "concurrently \"npm run proxy\" \"npm run dev\"", "test:db": "node scripts/test-db-connection.js", "test:db:proxy": "node scripts/test-db-connection-proxy.js", "test:db:direct": "node scripts/test-db-connection-direct.js", "test:accounts": "node scripts/test-posting-accounts.js", "save-credentials": "node scripts/save-credentials.js"}, "dependencies": {"@influxdata/influxdb-client": "^1.35.0", "@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.8", "@types/pg": "^8.15.2", "axios": "^1.9.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "framer-motion": "^12.12.1", "next": "15.3.1", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.3", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^8.2.2", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}