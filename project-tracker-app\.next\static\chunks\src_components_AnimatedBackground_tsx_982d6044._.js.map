{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Downloads/ab_testing_site-main/ab_testing_site-main/project-tracker-app/src/components/AnimatedBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useRef, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface Particle {\n  x: number;\n  y: number;\n  size: number;\n  speedX: number;\n  speedY: number;\n  color: string;\n}\n\nexport default function AnimatedBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particles: Particle[] = [];\n  const particleCount = 50;\n  const colors = ['#4f46e5', '#6366f1', '#818cf8', '#3b82f6', '#60a5fa'];\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    // Set canvas to full screen\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    // Initialize particles\n    const initParticles = () => {\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          size: Math.random() * 5 + 1,\n          speedX: (Math.random() - 0.5) * 0.5,\n          speedY: (Math.random() - 0.5) * 0.5,\n          color: colors[Math.floor(Math.random() * colors.length)]\n        });\n      }\n    };\n\n    // Animation loop\n    const animate = () => {\n      requestAnimationFrame(animate);\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      // Update and draw particles\n      for (let i = 0; i < particles.length; i++) {\n        const p = particles[i];\n        \n        // Move particles\n        p.x += p.speedX;\n        p.y += p.speedY;\n        \n        // Bounce off edges\n        if (p.x < 0 || p.x > canvas.width) p.speedX *= -1;\n        if (p.y < 0 || p.y > canvas.height) p.speedY *= -1;\n        \n        // Draw particle\n        ctx.beginPath();\n        ctx.arc(p.x, p.y, p.size, 0, Math.PI * 2);\n        ctx.fillStyle = p.color;\n        ctx.globalAlpha = 0.7;\n        ctx.fill();\n        \n        // Draw connections\n        for (let j = i + 1; j < particles.length; j++) {\n          const p2 = particles[j];\n          const dx = p.x - p2.x;\n          const dy = p.y - p2.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n          \n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.strokeStyle = p.color;\n            ctx.globalAlpha = 0.2 * (1 - distance / 100);\n            ctx.lineWidth = 0.5;\n            ctx.moveTo(p.x, p.y);\n            ctx.lineTo(p2.x, p2.y);\n            ctx.stroke();\n          }\n        }\n      }\n    };\n\n    // Initialize\n    resizeCanvas();\n    initParticles();\n    animate();\n\n    // Handle resize\n    window.addEventListener('resize', resizeCanvas);\n    \n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n    };\n  }, []);\n\n  return (\n    <canvas \n      ref={canvasRef} \n      className=\"fixed top-0 left-0 w-full h-full -z-10\"\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAce,SAAS;;IACtB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,YAAwB,EAAE;IAChC,MAAM,gBAAgB;IACtB,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;KAAU;IAEtE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,IAAI,CAAC,QAAQ;YAEb,MAAM,MAAM,OAAO,UAAU,CAAC;YAC9B,IAAI,CAAC,KAAK;YAEV,4BAA4B;YAC5B,MAAM;6DAAe;oBACnB,OAAO,KAAK,GAAG,OAAO,UAAU;oBAChC,OAAO,MAAM,GAAG,OAAO,WAAW;gBACpC;;YAEA,uBAAuB;YACvB,MAAM;8DAAgB;oBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;wBACtC,UAAU,IAAI,CAAC;4BACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;4BAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;4BAChC,MAAM,KAAK,MAAM,KAAK,IAAI;4BAC1B,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAChC,QAAQ,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAChC,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;wBAC1D;oBACF;gBACF;;YAEA,iBAAiB;YACjB,MAAM;wDAAU;oBACd,sBAAsB;oBACtB,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;oBAE/C,4BAA4B;oBAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;wBACzC,MAAM,IAAI,SAAS,CAAC,EAAE;wBAEtB,iBAAiB;wBACjB,EAAE,CAAC,IAAI,EAAE,MAAM;wBACf,EAAE,CAAC,IAAI,EAAE,MAAM;wBAEf,mBAAmB;wBACnB,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,OAAO,KAAK,EAAE,EAAE,MAAM,IAAI,CAAC;wBAChD,IAAI,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,OAAO,MAAM,EAAE,EAAE,MAAM,IAAI,CAAC;wBAEjD,gBAAgB;wBAChB,IAAI,SAAS;wBACb,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;wBACvC,IAAI,SAAS,GAAG,EAAE,KAAK;wBACvB,IAAI,WAAW,GAAG;wBAClB,IAAI,IAAI;wBAER,mBAAmB;wBACnB,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;4BAC7C,MAAM,KAAK,SAAS,CAAC,EAAE;4BACvB,MAAM,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;4BACrB,MAAM,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC;4BACrB,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;4BAE1C,IAAI,WAAW,KAAK;gCAClB,IAAI,SAAS;gCACb,IAAI,WAAW,GAAG,EAAE,KAAK;gCACzB,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,WAAW,GAAG;gCAC3C,IAAI,SAAS,GAAG;gCAChB,IAAI,MAAM,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;gCACnB,IAAI,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;gCACrB,IAAI,MAAM;4BACZ;wBACF;oBACF;gBACF;;YAEA,aAAa;YACb;YACA;YACA;YAEA,gBAAgB;YAChB,OAAO,gBAAgB,CAAC,UAAU;YAElC;gDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;uCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;;;;;AAGhB;GAhGwB;KAAA", "debugId": null}}]}